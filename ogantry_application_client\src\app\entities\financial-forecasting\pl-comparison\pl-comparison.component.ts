import { FormControl } from '@angular/forms';
import { GlobalDetailSubCategory, GlobalDetailTaggingCategory, GlobalDetailTags, QueryFilter, SubCategory, TagCategory } from './../../administration/administration.model';
import { DatePipe, KeyValue } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, ViewChild } from '@angular/core';
import { MatSidenav } from '@angular/material/sidenav';
import { FINANCIAL_REVIEW_TYPES, MONTH_NAMES, TableHeader } from '@entities/project/financial-review/financial-review.model';
import { BillingTypes, Project, ProjectList, ValidMonthlyProjection } from '@entities/project/project.model';
import { ProjectService } from '@entities/project/project.service';
import { FilterReport, IFilter, QueryFilterParams } from '@entities/utilization-management/utilization.model';
import { UtilizationService } from '@entities/utilization-management/utilization.service';
import { SflBaseComponent } from '@shared/components/sfl-base/sfl-base.component';
import { ButtonParams } from '@shared/models';
import { SidebarParams } from '@shared/models/sidebar-params.model';
import { KtDialogService } from '@shared/services';
import { TreeNode } from 'primeng/api';
import moment from 'moment';
import { LayoutConfigService } from '@shared/services/layout-config.service';
import { AppConstants, AppMessages } from '@shared/constants';
import { ISavedFilterList, SaveFilter } from '@entities/administration/administration.model';
import { AlertType } from '@shared/models/alert-type.enum';
import { LayoutUtilsService } from '@shared/services/layout-utils.service';
import { GetSetCacheFiltersService } from '@shared/services/get-set-cache-filters.service';
import { BehaviorSubject, interval, of, Subscription, throwError } from 'rxjs';
import { AddCommasToNumbersPipe } from '@shared/pipes/add-commas-to-numbers.pipe';
import { catchError, delay, map, retryWhen, switchMap, take } from 'rxjs/operators';
import * as _ from 'lodash';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { PNGTree, TreeViewStructure } from '@entities/administration/append-tags/tree-view-model';
import { AdministrationService } from '@entities/administration/administration.service';
import { TreeNode as TagsTreeNode } from '@entities/administration/append-tags/tree-view-model';
import { AuthService } from '@auth/index';

@Component({
  selector: 'app-pl-comparison',
  templateUrl: './pl-comparison.component.html',
  styleUrls: ['./pl-comparison.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PlComparisonComponent extends SflBaseComponent implements OnInit, AfterViewInit {
  showExportOptions = false;
  showExportOptionDialog = false;
  noDataFound = false;
  openFilter = false;
  cardTitle = 'P&L Comparison';
  cardSubTitle = null;
  splitButtonDropDownOption = {
    action: this.openSideBar.bind(this),
    options: [
      {
        label: 'Get Stored Filters',
        icon: 'get-stored-filter-split-button-icon',
        command: () => {
          this.openSaveFilterList();
        }
      },
      {
        label: 'Save Filter',
        icon: 'save-filter-split-button-icon',
        command: () => {
          this.onSaveFilter();
        }
      },
      {
        label: 'Reset Filter',
        icon: 'reset-filter-split-button-icon',
        command: () => {
          this.resetFilter();
        }
      }
    ]
  };
  sidebarButtons: ButtonParams[] = [
    {
      btnSvg: 'filter-list',
      btnClass: 'btn-filter-icon',
      action: this.openSaveFilterList.bind(this)
    },
    {
      btnSvg: 'save',
      btnClass: 'btn btn-sm btn-icon btn-icon-light svg-icon svg-icon-md icon-background mr-2 filter-btn-wrapper',
      action: this.onSaveFilter.bind(this)
    }
  ];
  // For Future Use
  exportButtons: ButtonParams[] = [
    {
      action: this.exportReport.bind(this)
    }
  ];

  buttons: ButtonParams[] = [
    {
      btnSvg: 'download-wt',
      btnClass: 'btn-filter-icon download',
      action: this.openExportOptionList.bind(this)
    }
  ];

  tags = [];
  wizard = new KTWizard();
  @ViewChild('wizard1', { static: true }) wz: ElementRef;
  sidebarParams: SidebarParams<FilterReport>;
  @ViewChild('sidebarFilter', { static: true }) el: MatSidenav;

  //declarations to generate data
  finalProjectionData: TreeNode[] = [];
  projectionData1: TreeNode[] = [];
  projectionData2: TreeNode[] = [];

  projections: any;
  projection1: any;
  projection2: any;
  @Input() projectId?: number;
  @Input() project: Project;

  tableHeaders: TableHeader[] = [];
  clientGroups;
  projectGroups;
  projectsRevenue = [];
  projectsExpense = [];
  projectsRevenue1 = [];
  projectsExpense1 = [];
  projectsRevenue2 = [];
  projectsExpense2 = [];
  bench = [];
  bench1 = [];
  bench2 = [];
  workExceptions = [];
  workExceptions1 = [];
  workExceptions2 = [];
  fixedRevenueTreeData: TreeNode[] = [];
  tAndMRevenueTreeData: TreeNode[] = [];
  fixedRevenueTreeData1: TreeNode[] = [];
  tAndMRevenueTreeData1: TreeNode[] = [];
  fixedRevenueTreeData2: TreeNode[] = [];
  tAndMRevenueTreeData2: TreeNode[] = [];
  revenueTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
    children: []
  };
  expenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
    children: []
  };
  grossProfitTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
    children: []
  };
  grossMarginTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
    children: []
  };
  revenueTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
    children: []
  };
  expenseTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
    children: []
  };
  grossProfitTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
    children: []
  };
  grossMarginTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
    children: []
  };
  revenueTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
    children: []
  };
  expenseTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
    children: []
  };
  grossProfitTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
    children: []
  };
  grossMarginTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
    children: []
  };
  benchTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
    children: []
  };
  benchTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
    children: []
  };
  benchTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
    children: []
  };
  projectExpenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
    children: []
  };
  projectExpenseTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
    children: []
  };
  projectExpenseTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
    children: []
  };
  WorkExpenseTreeData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
    children: []
  };
  WorkExpenseTreeData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
    children: []
  };
  WorkExpenseTreeData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
    children: []
  };
  pLAdjustData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
  };
  pLAdjustData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
  };
  pLAdjustData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
  };
  cOGSAdjust: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
  };
  cOGSAdjust1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
  };
  cOGSAdjust2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
  };
  sgaAdjustData: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
  };
  sgaAdjustData1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
  };
  sgaAdjustData2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
  };
  netProfit: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
  };
  netMargin: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
  };
  netProfit1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
  };
  netMargin1: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
  };
  netProfit2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
  };
  netMargin2: TreeNode = {
    data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
  };
  peopleExpenseTreeData: TreeNode[] = [];
  grossProfitData: TreeNode[] = [];
  grossMarginData: TreeNode[] = [];
  grossProfitTotal: number;
  revenueTotal: number;
  peopleExpenseTreeData1: TreeNode[] = [];
  grossProfitData1: TreeNode[] = [];
  grossMarginData1: TreeNode[] = [];
  grossProfitTotal1: number;
  revenueTotal1: number;
  peopleExpenseTreeData2: TreeNode[] = [];
  grossProfitData2: TreeNode[] = [];
  grossMarginData2: TreeNode[] = [];
  grossProfitTotal2: number;
  revenueTotal2: number;
  dataFilter: IFilter = new IFilter();
  statuses = [];
  defaultStatuses: string[];
  frozenCols = [];
  height = 'calc((var(--fixed-content-height, 1vh) * 100) - 100px)';
  resizeFlag = false;
  showFilterListDialog = false;
  availableFilters = null;
  selectedFilter = null;
  client = [];
  activeEmployee = [];
  loadingEmp = false;
  loading$$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);
  effectiveDate1 = null;
  effectiveDate2 = null;
  projects = [];
  sharedFilters: QueryFilter[] = [];
  myFilters: QueryFilter[] = [];
  showSavedFilter = false;
  selectedFilterFormControl = new FormControl('');
  editFilterObj: QueryFilter;
  showNameError = false;
  showDeleteDialog = false;
  deleteFilterObj: QueryFilter;
  showShareDialog = false;
  shareFilterObj = null;
  showEditDialog = false;
  filteredFilters: ISavedFilterList;
  currentDate: String;
  queryFilterId: number;
  minEffectiveDate: Date;
  tagCategories: TagCategory[] = [];
  taggingTags = [];
  globalDetailsTaggingCategory: GlobalDetailTaggingCategory;
  tagSubCategory: SubCategory[] = [];
  globalDetailsTagSubCategory: GlobalDetailSubCategory;
  globalDetailsTag: GlobalDetailTags;
  groupedCategory: TreeViewStructure;
  selectedTags = [];
  costConstant = 'cost';
  showPausedProjectDialog = false;
  pausedProjectList = [];
  isResumeValidationInProgress = false;
  private messageInterval: Subscription;
  private currentMessageIndex = 0;
  rotatingMessage = '';
  calculatingProjectName = '';
  isAllowedNetMargin = false;

  constructor(
    private readonly projectService: ProjectService,
    readonly utilizationService: UtilizationService,
    private readonly cdf: ChangeDetectorRef,
    readonly datePipe: DatePipe,
    readonly commaNumberPipe: AddCommasToNumbersPipe,
    private readonly ktDialogService: KtDialogService,
    private readonly layoutConfigService: LayoutConfigService,
    private readonly layoutUtilsService: LayoutUtilsService,
    private readonly cacheFilter: GetSetCacheFiltersService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly adminService: AdministrationService,
    private readonly authService: AuthService
  ) {
    super();
  }

  ngOnInit() {
    if (window.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
    if (this.cacheFilter.getCacheFilters('Compare-PL')) {
      this.dataFilter = this.cacheFilter.getCacheFilters('Compare-PL');
    }
    this.applyAllFilter();
    this.frozenCols = [{ field: 'type', monthLabel: 'Type' }];
    this.getClientGroup();
    this.getProjectGroup();
    // we need to stop the initial project load as sometimes it is time consuming if user has to apply entire new filter even then they need to wait till the current projection ends.
    // this.getProjections();
    this.loading$.next(false);
    this.getProjectStatus();
    this.getStoredFilters();
    this.getClient();
    this.getProjectList();
    this.getCategoryMasterData();
    this.checkNetMarginPermission();
  }

  getCategoryMasterData() {
    this.tagCategories = [];
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getTagCategories('TagCategoryManagement').subscribe(
        (res) => {
          this.loading$.next(false);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagCategoryManagement') {
              this.globalDetailsTaggingCategory = globalDetail[0];
              this.tagCategories = globalDetail[0].global_detail.extended_fields.tagCategory;
              this.adminService.setTagCategories(globalDetail[0].global_detail);
              this.getTagSubCategories();
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  getTagSubCategories() {
    this.tagSubCategory = [];
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getTagSubCategories('SubCategoryManagement').subscribe(
        (res) => {
          this.loading$.next(false);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'SubCategoryManagement') {
              this.globalDetailsTagSubCategory = globalDetail[0];
              this.tagSubCategory = globalDetail[0].global_detail.extended_fields.subCategory;
              this.adminService.setTagSubCategories(globalDetail[0].global_detail);
              this.getGlobalDetailTags();
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  getGlobalDetailTags() {
    this.taggingTags = [];
    this.loading$.next(true);
    this.subscriptionManager.add(
      this.adminService.getTags('TagManagement').subscribe(
        (res) => {
          this.loading$.next(false);
          if (res?.data?.global_details) {
            const globalDetail = res?.data?.global_details;
            if (globalDetail && globalDetail[0]?.global_detail?.name === 'TagManagement') {
              this.globalDetailsTag = globalDetail[0];
              this.taggingTags = globalDetail[0].global_detail.extended_fields.tags;
              this.adminService.setTags(globalDetail[0].global_detail);
              this.combineCategoryAndSubCategory();
            }
          }
        },
        () => this.loading$.next(false)
      )
    );
  }

  combineCategoryAndSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
    this.injectTagsToRespectiveCategoryOrSubCategory();
    this.initGroupingCategoryTags();
  }

  injectTagsToRespectiveCategoryOrSubCategory() {
    for (const category of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory) {
      for (const tag of this.taggingTags) {
        if (tag.tagCategory === category.id) {
          const subCateIndex = category.subTagCategory.findIndex((subCate) => subCate.id === tag.subTagCategory);
          if (subCateIndex !== -1) {
            category.subTagCategory[subCateIndex]['tags'].push(tag);
          } else {
            category.tags.push(tag);
          }
        }
      }
      category.subTagCategory = [...this.tagSubCategory?.filter((subCate) => subCate?.parentCategoryId === category?.id)];
    }
  }

  initGroupingCategoryTags() {
    this.groupedCategory = { data: [] };
    for (const [index, category] of this.globalDetailsTaggingCategory.global_detail.extended_fields.tagCategory.entries()) {
      const dataCollection: TagsTreeNode = new TagsTreeNode();
      dataCollection.label = category.name;
      dataCollection.selectable = false;
      dataCollection.collapsedIcon = 'pi-chevron-right';
      dataCollection.expandedIcon = 'pi-chevron-down';
      dataCollection.expanded = true;
      if (category?.subTagCategory?.length) {
        for (const [subIndex, subCate] of category?.subTagCategory?.entries()) {
          dataCollection.children.push({
            label: subCate.name,
            collapsedIcon: 'pi-chevron-right',
            expandedIcon: 'pi-chevron-down',
            children: [],
            selectable: false,
            expanded: true
          });
          if (subCate?.tags?.length) {
            for (const [tagIndex, tag] of subCate?.tags?.entries()) {
              dataCollection.children[subIndex]?.children?.push({ label: tag.name, collapsedIcon: 'pi-chevron-right', expandedIcon: 'pi-chevron-down', expanded: true });
            }
          }
        }
      }
      if (category?.tags?.length) {
        for (const [tagIndex, tag] of category?.tags?.entries()) {
          dataCollection.children?.push({ label: tag.name, collapsedIcon: 'pi-chevron-right', expandedIcon: 'pi-chevron-down', expanded: true });
        }
      }
      this.groupedCategory.data.push(dataCollection);
    }
    this.onFilterChangePreapareSelectedTreeNodes();
  }

  getExtractedTags(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 1];
  }

  onFilterChangePreapareSelectedTreeNodes() {
    if (this.dataFilter.tags?.length) {
      this.selectedTags = [];
      const tagsWithCategory = this.dataFilter.tags.split(',');
      for (const tag of tagsWithCategory) {
        const pngTreeItem: PNGTree = new PNGTree();
        const tagLength = tag.split('__');
        pngTreeItem.collapsedIcon = 'pi-chevron-right';
        pngTreeItem.expandedIcon = 'pi-chevron-down';
        pngTreeItem.label = this.getExtractedTags(tag);
        pngTreeItem.expanded = true;
        pngTreeItem.parent = {
          label: this.getExtractedTagsParentCategory(tag),
          children: [],
          collapsedIcon: 'pi-chevron-right',
          expandedIcon: 'pi-chevron-down',
          expanded: true,
          parent:
            tagLength?.length > 2
              ? {
                  label: this.getExtractedTagsParentCategory(tag),
                  children: [],
                  collapsedIcon: 'pi-chevron-right',
                  expandedIcon: 'pi-chevron-down',
                  expanded: true,
                  parent: undefined
                }
              : undefined
        };
        if (this.groupedCategory) {
          for (const parent of this.groupedCategory.data) {
            for (const children of parent.children) {
              for (const children_data of children.children) {
                if (children_data.label === pngTreeItem.label) {
                  this.selectedTags.push(children_data);
                }
              }
            }
          }
        }
      }
      this.cdf.detectChanges();
    }
  }

  getExtractedTagsParentCategory(tagWithCategory: string): string {
    const tagArray = tagWithCategory.split('__');
    return tagArray[tagArray.length - 2];
  }

  async ngAfterViewInit() {
    if (this.wz) {
      // Initialize form wizard
      this.wizard = new KTWizard(this.wz.nativeElement, {
        startStep: 1,
        clickableSteps: true
      });

      // Change event
      this.wizard.on('change', (wizard) => {
        setTimeout(() => {
          KTUtil.scrollTop();
          this.layoutConfigService.updateHeight$.next(true);
          this.cdf.detectChanges();
        }, 500);
      });
      if (this.el) {
        this.sidebarParams = { template: this.el };
        this.openFilter = true;
      }
      await this.routerListener();
      if (this.queryFilterId) {
        this.sidebarParams.template.close();
      }
    }
  }

  defaultFilters() {
    if (!this.dataFilter?.statuses) {
      this.dataFilter.statuses = this.defaultStatuses.toString();
      this.dataFilter.status = this.defaultStatuses;
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.statuses
      });
    }
    if (this.dataFilter.include_utilizations === null || this.dataFilter.include_utilizations === undefined) {
      this.dataFilter.include_utilizations = true;
      this.tags.push({
        label: 'Include Bench',
        value: 'True',
        key: ['include_utilizations']
      });
    }
    if (this.dataFilter.include_pl_plugs === null || this.dataFilter.include_pl_plugs === undefined) {
      this.dataFilter.include_pl_plugs = true;
      this.tags.push({
        label: 'Include P&L Plugs',
        value: 'True',
        key: ['include_pl_plugs']
      });
    }

    if (!this.dataFilter.start_date && !this.dataFilter.end_date) {
      this.dataFilter.rollingOption = 'Current plus 2 months';
      if (this.dataFilter.rollingOption) {
        this.tags.push({
          label: 'Rolling',
          value: this.dataFilter.rollingOption.toString(),
          key: ['start_date', 'end_date', 'rollingOption']
        });
      }
    }
    if (this.dataFilter.variance) {
      this.dataFilter.varianceValue = this.dataFilter.varianceValue || 30;
      this.dataFilter.showCustomVariancePeriod = false;
      this.dataFilter.effective_date1 = new Date(new Date().setDate(new Date().getDate() - this.dataFilter.varianceValue));
      if (!this.dataFilter.effective_date1) {
        this.tags.push({
          label: 'Historical Data',
          value: this.datePipe.transform(this.dataFilter.effective_date1, 'MM/dd/yyyy, hh:mm a'),
          key: ['dataType1', 'effective_date1', 'dataType2', 'effective_date2']
        });
      }
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize(event) {
    if (event.target.innerWidth <= 1024) {
      this.resizeFlag = true;
    } else {
      this.resizeFlag = false;
    }
  }

  getProjectStatus() {
    this.subscriptionManager.add(
      this.projectService.getProjectStatus().subscribe((res) => {
        const projectStatuses = res.data?.project_statuses || [];
        this.defaultStatuses = projectStatuses.filter((status) => status.project_status.is_default).map((status) => status.project_status.name);

        this.statuses = projectStatuses.map((status) => ({
          label: status.project_status.name,
          value: status.project_status.name
        }));
        this.defaultFilters();
      })
    );
  }

  getClientGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'customers'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const clientGrps = [{ label: '--None--', value: { name: '', value: '' } }];
        response.data.query_filters?.map((query) => {
          clientGrps.push({
            label: query.query_filter.name,
            value: { name: query.query_filter.name, value: query.query_filter.query_string }
          });
        });
        this.clientGroups = clientGrps;
        this.cdf.detectChanges();
      })
    );
  }

  getClient() {
    this.subscriptionManager.add(
      this.utilizationService.getClientData().subscribe((res) => {
        const client = [{ label: '--None--', value: { name: '', value: '' } }];
        res?.body?.data?.customers?.map((c) => {
          client.push({
            label: c.customer.name,
            value: { name: c.customer.name, value: String(c.customer.id) }
          });
        });
        this.client = client;
        this.cdf.detectChanges();
      })
    );
  }

  // used to sort the given list in alphabetical order
  sortList(sortList) {
    if (this.sortList.length > 0) {
      sortList.sort((a, b) => {
        const fa = a?.label?.toLowerCase();
        const fb = b?.label?.toLowerCase();
        if (fa < fb) {
          return -1;
        }
        if (fa > fb) {
          return 1;
        }
        return 0;
      });
    }
  }

  getProjectList() {
    this.subscriptionManager.add(
      this.utilizationService.getProjectList().subscribe((res) => {
        const projects = [{ label: '--None--', value: { name: '', value: '' } }];
        res.data.projects.forEach((project) => {
          projects.push({
            label: `${project?.project?.customer?.name} : ${project?.project?.name}`,
            value: { name: project.project.name, value: String(project.project.id) }
          });
        });
        this.projects = projects;
        this.sortList(this.projects);
        this.applyAllFilter();
        this.cdf.detectChanges();
      })
    );
  }

  getProjectGroup() {
    const requestObject = {
      // is_shared: true,
      resource: 'projects'
    };

    this.subscriptionManager.add(
      this.utilizationService.getGroup(requestObject).subscribe((res) => {
        const response = JSON.parse(JSON.stringify(res));
        const projectGrps = [{ label: '--None--', value: { name: '', value: '' } }];
        response.data.query_filters?.map((query) => {
          projectGrps.push({
            label: query.query_filter.name,
            value: { name: query.query_filter.name, value: query.query_filter.query_string }
          });
        });
        this.projectGroups = projectGrps;
        this.cdf.detectChanges();
      })
    );
  }

  getClientsIds() {
    const paramsToRemove = ['offset', 'limit'];
    const filteredParams = this.removeParams(this.dataFilter?.customer_name?.value.split('&'), paramsToRemove);
    if (this.dataFilter?.customer_name?.value) {
      this.subscriptionManager.add(
        this.utilizationService.getClientIds(filteredParams).subscribe((res) => {
          if (res?.customer_ids) {
            this.dataFilter.customer_ids = res.customer_ids.join(',');
          } else {
            this.dataFilter.customer_ids = '';
          }
          this.cdf.detectChanges();
        })
      );
    } else {
      this.dataFilter.customer_name = null;
      this.dataFilter.customer_ids = '';
    }
  }

  getProjectsIds() {
    const paramsToRemove = ['offset', 'limit'];
    const filteredParams = this.removeParams(this.dataFilter?.project_name?.value.split('&'), paramsToRemove);
    if (this.dataFilter?.project_name?.value) {
      this.subscriptionManager.add(
        this.utilizationService.getProjectIds(filteredParams).subscribe((res) => {
          if (res?.project_ids) {
            this.dataFilter.project_ids = res.project_ids.join(',');
          } else {
            this.dataFilter.project_ids = '';
          }
          this.cdf.detectChanges();
        })
      );
    } else {
      this.dataFilter.project_name = null;
      this.dataFilter.project_ids = '';
    }
  }

  removeParams(params, paramsToRemove) {
    return params
      .filter((param) => {
        const [key, value] = param.split('=');
        return !paramsToRemove.includes(key) && value !== '' && value !== null;
      })
      .join('&');
  }

  dateRangeCalculation() {
    if (this.dataFilter.rollingOption) {
      const { startDate, endDate } = this.getStartDateEndDateFromRolling(this.dataFilter.rollingOption);
      this.dataFilter.start_date = this.datePipe.transform(startDate, AppConstants.format);
      this.dataFilter.end_date = this.datePipe.transform(endDate, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.quarter = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }

    if (this.dataFilter.year) {
      const startEndDate = this.getStartEndDateFromYear(this.dataFilter.year);
      this.dataFilter.start_date = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.end_date = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.quarter = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
    if (this.dataFilter.quarter) {
      const startEndDate = this.getStartEndDateFromQuarter(this.dataFilter.quarter);
      this.dataFilter.start_date = this.datePipe.transform(startEndDate.start_date, AppConstants.format);
      this.dataFilter.end_date = this.datePipe.transform(startEndDate.end_date, AppConstants.format);
      this.dataFilter.year = null;
      this.dataFilter.rollingOption = null;
      this.dataFilter.start_month = null;
      this.dataFilter.end_month = null;
    }
  }

  async getProjections() {
    let queryFilter: QueryFilterParams = {};
    queryFilter.projection_detail_level = 'position_monthly';
    if (!this.dataFilter.start_date && !this.dataFilter.end_date) {
      const date = new Date();
      queryFilter.start_date = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth(), 1), AppConstants.format);
      queryFilter.end_date = this.datePipe.transform(new Date(date.getFullYear(), date.getMonth() + 3, 0), AppConstants.format);
    }
    if (this.dataFilter) {
      this.cacheFilter.setCacheFilters({ ...this.dataFilter, ...queryFilter }, 'Compare-PL');
      for (const [key] of Object.entries(this.dataFilter)) {
        if (this.dataFilter[key] && (key === 'start_date' || key === 'end_date' || key === 'effective_date1' || key === 'effective_date2')) {
          if (key === 'effective_date1') {
            queryFilter[`${key}`] = moment.utc(new Date(this.dataFilter[key])).format('YYYY-MM-DDTHH:mm:ss') + 'Z';
          } else if (key === 'effective_date2') {
            queryFilter[`${key}`] = moment.utc(new Date(this.dataFilter[key])).format('YYYY-MM-DDTHH:mm:ss') + 'Z';
          } else {
            queryFilter[`${key}`] = this.datePipe.transform(this.dataFilter[key], AppConstants.format);
          }
        } else {
          queryFilter[`${key}`] = this.dataFilter[key];
        }
      }
      if (this.dataFilter?.client?.value && this.dataFilter?.customer_ids) {
        queryFilter['customer_ids'] = this.dataFilter.customer_ids + ',' + this.dataFilter.client.value;
        delete queryFilter['client'];
      } else if (this.dataFilter?.client?.value) {
        queryFilter['customer_ids'] = Number(this.dataFilter.client.value);
        delete queryFilter['client'];
      }
      // converting the effective date 1 and effective date 2 to UTC
      // queryFilter.effective_date1 = moment.utc(this.dataFilter.effective_date1).format();
      // queryFilter.effective_date2 = moment.utc(this.dataFilter.effective_date2).format();
      delete queryFilter['dataType1'];
      delete queryFilter['dataType2'];
      delete queryFilter['customer_name'];
      delete queryFilter['project_name'];
      delete queryFilter['date'];
      delete queryFilter['year'];
      delete queryFilter['period'];
      delete queryFilter['quarter'];
      delete queryFilter['status'];
      delete queryFilter['start_month'];
      delete queryFilter['end_month'];
      delete queryFilter['selectedClient'];
      delete queryFilter['selectedProject'];
      delete queryFilter['showProjectFilter'];
      delete queryFilter['showClientFilter'];
      delete queryFilter['ClientName'];
      delete queryFilter['clientName'];
      delete queryFilter['projectName'];
      delete queryFilter['showDateRange'];
      delete queryFilter['showCustomVariancePeriod'];
      delete queryFilter['varianceValue'];
      delete queryFilter['variance'];
    }

    // converting the effective date 1 and effective date 2 to UTC
    // queryFilter.effective_date1 = moment.utc(this.dataFilter.effective_date1).format();
    // queryFilter.effective_date2 = moment.utc(this.dataFilter.effective_date2).format();
    queryFilter = this.queryStringUtil(queryFilter);
    this.loading$.next(false);
    // we will be calling the one api to gate all the effective dates based on this filter, then
    // we will compare if the effective date is older than the min date then we have to pass the min date we have otherwise the api would response with a failure.
    // This way user would gate data from the least available day.
    // await this.getEffectiveDates(queryFilter);
    // if the effective date is older than the min date then we have to pass the min date we have otherwise the api would response with a failure. This way user would gate data from the least available day.
    // if (new Date(this.dataFilter.effective_date1) < this.minEffectiveDate) {
    //   this.dataFilter.effective_date1 = this.minEffectiveDate;
    //   // converting the effective date 1 to UTC
    //   queryFilter.effective_date1 = moment.utc(this.dataFilter.effective_date1).format();
    // }
    this.loading$.next(true);
    this.finalProjectionData = [];
    this.effectiveDate1 = null;
    this.effectiveDate2 = null;
    let flag = 0;

    // Making param consistent for API call that has been stored in BE cache
    const entries = Object.entries(queryFilter);
    let consistentParam = {};
    const numericKeys = ['project_ids', 'customer_ids'];
    const splitAndSortKeys = ['statuses', 'tags'];
    entries.sort((a, b) => a[0].localeCompare(b[0]));

    for (const [key, value] of entries) {
      if (numericKeys.includes(key)) {
        const numericValues = value.split(',').map(Number);
        numericValues.sort((a, b) => a - b);
        consistentParam[key] = numericValues.join(',');
      } else if (splitAndSortKeys.includes(key)) {
        const nonNumericValues = value.split(',').sort();
        consistentParam[key] = nonNumericValues.join(',');
      } else {
        consistentParam[key] = value;
      }
    }

    const shouldContinue = await this.getPausedProjectList(consistentParam['project_ids']);
    if (!shouldContinue) {
      return;
    }
    this.loading$.next(true);
    this.noDataFound = false;
    this.subscriptionManager.add(
      this.projectService
        .getCompareProjections(consistentParam)
        .pipe(
          map((res) => {
            if (res.status === 202) {
              flag++;
              throw res;
            }
            return res;
          }),
          retryWhen((errors) =>
            errors.pipe(
              switchMap((val) => {
                // retry only when res.status code is 202 and we won't keep on calling the service for other status code
                if (flag >= 15 && (val.status === 202 || val?.error?.status === 202)) {
                  return of(val).pipe(delay(15000), take(2));
                } else if (flag < 15 && (val.status === 202 || val?.error?.status === 202)) {
                  return of(val).pipe(delay(1000), take(15));
                } else {
                  return throwError(errors);
                }
              })
            )
          ),
          catchError((err) => throwError(err))
        )
        .subscribe({
          next: async (res) => {
            if (res.body['message'] !== 'No results found (projection1)') {
              this.projections = res.body['data'];
              await this.sortByProjectName(this.projections.projections[2]?.projections_diff?.projects, true);
              await this.sortByProjectName(this.projections?.projections[0]?.projection1?.projects, false);
              await this.sortByProjectName(this.projections?.projections[1]?.projection2?.projects, false);
              this.makeRowsSameHeight();
              this.prepareTableHeaders();
              this.loading$.next(false);
              this.cdf.detectChanges();
              if (res?.body['message'] && this.isPartiallyMatch(res?.body['message'])) {
                this.noDataFound = true;
              }
            } else {
              if (res?.body['message'] && this.isPartiallyMatch(res?.body['message'])) {
                this.noDataFound = true;
              }
              this.loading$.next(false);
            }
          },
          complete: () => this.loading$.next(false)
        })
    );
  }

  makeRowsSameHeight() {
    setTimeout(() => {
      if (document.getElementsByClassName('p-treetable-scrollable-wrapper').length) {
        const wrapper = document.getElementsByClassName('p-treetable-scrollable-wrapper');
        for (var i = 0; i < wrapper.length; i++) {
          const w = wrapper.item(i) as HTMLElement;
          const frozen_rows: any = w.querySelectorAll('.p-treetable-frozen-view tr');
          const unfrozen_rows: any = w.querySelectorAll('.p-treetable-unfrozen-view tr');
          for (let i = 0; i < frozen_rows.length; i++) {
            if (frozen_rows[i].clientHeight > unfrozen_rows[i].clientHeight) {
              unfrozen_rows[i].style.height = frozen_rows[i].clientHeight + 'px';
            } else if (frozen_rows[i].clientHeight < unfrozen_rows[i].clientHeight) {
              frozen_rows[i].style.height = unfrozen_rows[i].clientHeight + 'px';
            }
          }
        }
        this.layoutConfigService.updateHeight$.next(true);
        this.height = 'calc((var(--fixed-content-height, 1vh) * 100) - 160px)';
      }
    }, 0);
  }

  async sortByProjectName(data?: any, isCompareData = false): Promise<void> {
    return new Promise((resolve) => {
      const target = data ? data : this.projections.projections[2]?.projections_diff?.projects;

      if (Array.isArray(target) && target.length > 0) {
        target.sort((a, b) => {
          const customerA = a?.project?.customer?.name?.toLowerCase() || '';
          const customerB = b?.project?.customer?.name?.toLowerCase() || '';

          if (customerA !== customerB) {
            return customerA.localeCompare(customerB);
          }

          let projectNameA = '';
          let projectNameB = '';
          if (isCompareData) {
            projectNameA = a?.project?.name?.diff?.toLowerCase() || '';
            projectNameB = b?.project?.name?.diff?.toLowerCase() || '';
          } else {
            projectNameA = a?.project?.name?.toLowerCase() || '';
            projectNameB = b?.project?.name?.toLowerCase() || '';
          }

          return projectNameA.localeCompare(projectNameB);
        });
      }
      resolve();
    });
  }

  async sortByPositionNameDiff(): Promise<void> {
    return new Promise((resolve) => {
      this.projections?.projections[2]?.projections_diff?.projects?.forEach((projects) => {
        const valid_monthly_projections = projects?.project?.projection?.valid_monthly_projections;
        valid_monthly_projections?.forEach((projection) => {
          if (projection) {
            const valid_positions = projection?.valid_monthly_projection?.validated_monthly_positions;
            const positionWithoutProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name?.diff !== 'project_expenses');
            const positionWithProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name?.diff === 'project_expenses');
            positionWithoutProjectExpense?.sort((a, b) => {
              const fa = a?.validated_monthly_position?.position?.name?.diff?.toLowerCase();
              const fb = b?.validated_monthly_position?.position?.name?.diff?.toLowerCase();

              if (fa < fb) {
                return -1;
              }
              if (fa > fb) {
                return 1;
              }
              return 0;
            });
            positionWithoutProjectExpense?.push(positionWithProjectExpense[0]);
            projection.valid_monthly_projection.validated_monthly_positions = positionWithoutProjectExpense;
          }
        });
      });
      resolve();
    });
  }

  async sortByPositionName1(): Promise<void> {
    return new Promise((resolve) => {
      this.projections?.projections[0]?.projection1?.projects?.forEach((projects) => {
        const valid_monthly_projections = projects?.project?.projection?.valid_monthly_projections;
        valid_monthly_projections?.forEach((projection) => {
          if (projection) {
            const valid_positions = projection?.valid_monthly_projection?.validated_monthly_positions;
            const positionWithoutProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name !== 'project_expenses');
            const positionWithProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name === 'project_expenses');
            positionWithoutProjectExpense?.sort((a, b) => {
              const fa = a?.validated_monthly_position?.position?.name?.toLowerCase();
              const fb = b?.validated_monthly_position?.position?.name?.toLowerCase();

              if (fa < fb) {
                return -1;
              }
              if (fa > fb) {
                return 1;
              }
              return 0;
            });
            positionWithoutProjectExpense?.push(positionWithProjectExpense[0]);
            projection.valid_monthly_projection.validated_monthly_positions = positionWithoutProjectExpense;
          }
        });
      });
      resolve();
    });
  }

  async sortByPositionName2(): Promise<void> {
    return new Promise((resolve) => {
      this.projections?.projections[1]?.projection2?.projects?.forEach((projects) => {
        const valid_monthly_projections = projects?.project?.projection?.valid_monthly_projections;
        valid_monthly_projections?.forEach((projection) => {
          if (projection) {
            const valid_positions = projection?.valid_monthly_projection?.validated_monthly_positions;
            const positionWithoutProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name !== 'project_expenses');
            const positionWithProjectExpense = valid_positions?.filter((position) => position.validated_monthly_position?.position?.name === 'project_expenses');
            positionWithoutProjectExpense?.sort((a, b) => {
              const fa = a?.validated_monthly_position?.position?.name?.toLowerCase();
              const fb = b?.validated_monthly_position?.position?.name?.toLowerCase();

              if (fa < fb) {
                return -1;
              }
              if (fa > fb) {
                return 1;
              }
              return 0;
            });
            positionWithoutProjectExpense?.push(positionWithProjectExpense[0]);
            projection.valid_monthly_projection.validated_monthly_positions = positionWithoutProjectExpense;
          }
        });
      });
      resolve();
    });
  }

  ngOnDestroy() {
    this.ktDialogService.hide();
  }

  prepareTableHeaders() {
    this.tableHeaders = [];
    this.projectsRevenue = [];
    this.projectsExpense = [];
    this.projectsRevenue1 = [];
    this.projectsExpense1 = [];
    this.projectsRevenue2 = [];
    this.projectsExpense2 = [];
    this.bench = [];
    this.bench1 = [];
    this.bench2 = [];
    this.workExceptions = [];
    this.workExceptions1 = [];
    this.workExceptions2 = [];
    this.revenueTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
      children: []
    };
    this.expenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
      children: []
    };
    this.grossProfitTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
      children: []
    };
    this.grossMarginTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
      children: []
    };
    this.revenueTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
      children: []
    };
    this.expenseTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
      children: []
    };
    this.grossProfitTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
      children: []
    };
    this.grossMarginTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
      children: []
    };
    this.revenueTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.REVENUE },
      children: []
    };
    this.expenseTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.EXPENSE },
      children: []
    };
    this.grossProfitTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_PROFIT },
      children: []
    };
    this.grossMarginTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.GROSS_MARGIN },
      children: []
    };
    this.benchTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
      children: []
    };
    this.benchTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
      children: []
    };
    this.benchTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.BENCH },
      children: []
    };
    this.projectExpenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
      children: []
    };
    this.WorkExpenseTreeData = {
      data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
      children: []
    };
    this.WorkExpenseTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
      children: []
    };
    this.WorkExpenseTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION },
      children: []
    };
    this.pLAdjustData = {
      data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
    };
    this.cOGSAdjust = {
      data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
    };
    this.sgaAdjustData = {
      data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
    };
    this.netProfit = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
    };
    this.netMargin = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
    };
    this.projectExpenseTreeData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
      children: []
    };
    this.pLAdjustData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
    };
    this.cOGSAdjust1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
    };
    this.sgaAdjustData1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
    };
    this.netProfit1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
    };
    this.netMargin1 = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
    };
    this.projectExpenseTreeData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.PROJECTS },
      children: []
    };
    this.pLAdjustData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.PL_ADJUST }
    };
    this.cOGSAdjust2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.COGS_ADJUST }
    };
    this.sgaAdjustData2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.SGA_ADJUST }
    };
    this.netProfit2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_PROFIT }
    };
    this.netMargin2 = {
      data: { type: FINANCIAL_REVIEW_TYPES.NET_MARGIN }
    };
    this.fixedRevenueTreeData = [];
    this.tAndMRevenueTreeData = [];
    this.peopleExpenseTreeData = [];
    this.grossMarginData = [];
    this.grossProfitData = [];
    this.fixedRevenueTreeData1 = [];
    this.tAndMRevenueTreeData1 = [];
    this.peopleExpenseTreeData1 = [];
    this.grossMarginData1 = [];
    this.grossProfitData1 = [];
    this.fixedRevenueTreeData2 = [];
    this.tAndMRevenueTreeData2 = [];
    this.peopleExpenseTreeData2 = [];
    this.grossMarginData2 = [];
    this.grossProfitData2 = [];

    if (this.projections?.projections?.length) {
      let projection1 = this.projections.projections[0].projection1;

      let projection2 = this.projections.projections[1].projection2;

      let compareprojections = this.projections.projections[2].projections_diff;

      this.effectiveDate1 = 'Most recent change for this P&L was calculated on ' + this.datePipe.transform(this.projections.projections[0].effective_date, 'MM/dd/yyyy hh:mm');
      this.effectiveDate2 = 'Most recent change for this P&L was calculated on ' + this.datePipe.transform(this.projections.projections[1].effective_date, 'MM/dd/yyyy hh:mm');
      this.currentDate = this.datePipe.transform(new Date(), 'MM/dd/yyyy hh:mm');

      for (let i = 0; i < compareprojections?.projects?.length; i++) {
        this.fixedRevenueTreeData[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.FIXED_REVENUE }
        };
        this.projectsRevenue[i] = {
          data: { type: compareprojections.projects[i].project?.customer?.name, name: compareprojections.projects[i].project.name.diff },
          children: []
        };
        this.projectsExpense[i] = {
          data: { type: compareprojections.projects[i].project?.customer?.name, name: compareprojections.projects[i].project.name.diff },
          children: []
        };
        this.tAndMRevenueTreeData[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.T_M_REVENUE },
          children: []
        };
        this.peopleExpenseTreeData[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.PEOPLE_EXPENSE },
          children: []
        };
        this.grossProfitData[i] = {
          data: { type: compareprojections.projects[i].project?.customer?.name, name: compareprojections.projects[i].project.name.diff }
        };

        this.grossMarginData[i] = {
          data: { type: compareprojections.projects[i].project?.customer?.name, name: compareprojections.projects[i].project.name.diff }
        };

        for (const validMonthlyProjection of compareprojections?.projects[i]?.project?.projection?.valid_monthly_projections) {
          const monthlyProjection = validMonthlyProjection?.valid_monthly_projection;
          const effective_date_month = monthlyProjection?.month?.effective_date_1 === 0 ? monthlyProjection?.month?.effective_date_2 : monthlyProjection?.month?.effective_date_1;
          const effective_date_year = monthlyProjection?.year?.effective_date_1 === 0 ? monthlyProjection?.year?.effective_date_2 : monthlyProjection?.year?.effective_date_1;
          if (monthlyProjection) {
            const month = effective_date_month?.toString().length === 2 ? effective_date_month : '0' + effective_date_month;
            const tableHeader: TableHeader = {
              month: effective_date_month,
              monthLabel: `${MONTH_NAMES[effective_date_month - 1]} ${effective_date_year}`,
              year: effective_date_year,
              id: Number(`${effective_date_year}${month}`)
            };

            //add header only if month not already exist and maintain sort order too
            if (!this.tableHeaders.find((header) => header.id === tableHeader.id)) {
              this.tableHeaders = [...this.tableHeaders, tableHeader];
            }

            this.prepareTreeRowData(i, tableHeader, monthlyProjection);
          }
        }
        for (let i = 0; i < compareprojections?.utilizations?.length; i++) {
          this.bench[i] = {
            data: {
              type: `${compareprojections?.utilizations[i]?.employee?.first_name?.effective_date_1} ${
                compareprojections?.utilizations[i]?.employee?.last_name?.effective_date_1 || ''
              }`,
              id: `${compareprojections?.utilizations[i]?.employee?.id?.effective_date_1}`
            },
            children: []
          };
          for (const utilization of compareprojections?.utilizations[i]?.employee?.utilizations) {
            const monthlyUtilization = utilization?.utilization;
            if (monthlyUtilization) {
              const effective_date_month =
                monthlyUtilization?.month?.effective_date_1 === 0 ? monthlyUtilization?.month?.effective_date_2 : monthlyUtilization?.month?.effective_date_1;
              const effective_date_year =
                monthlyUtilization?.year?.effective_date_1 === 0 ? monthlyUtilization?.year?.effective_date_2 : monthlyUtilization?.year?.effective_date_1;

              const month = effective_date_month?.toString().length === 2 ? effective_date_month : '0' + effective_date_month;
              const tableHeader: TableHeader = {
                month: effective_date_month,
                monthLabel: `${MONTH_NAMES[effective_date_month - 1]} ${effective_date_year}`,
                year: effective_date_year,
                id: Number(`${effective_date_year}${month}`)
              };

              this.prepareBenchTreeRowData(i, tableHeader, monthlyUtilization);
            }
          }
        }
        for (let i = 0; i < compareprojections.employees_plus_work_exceptions.length; i++) {
          this.workExceptions[i] = {
            data: {
              type: `${compareprojections?.employees_plus_work_exceptions[i]?.employee?.first_name?.effective_date_1} ${
                compareprojections?.employees_plus_work_exceptions[i]?.employee?.last_name?.effective_date_1 || ''
              }`,
              id: `${compareprojections?.employees_plus_work_exceptions[i]?.employee?.id?.effective_date_1}`
            },
            children: []
          };
          for (const workExceptions of compareprojections?.employees_plus_work_exceptions[i]?.employee.work_exception_summary) {
            const monthlyUtilization = workExceptions.work_exceptions[0]?.work_exception;
            if (monthlyUtilization) {
              const year = monthlyUtilization.date.effective_date_1.split('-')[0];
              const month = monthlyUtilization.date.effective_date_1.split('-')[1];
              const tableHeader: TableHeader = {
                month: month,
                monthLabel: `${MONTH_NAMES[+month - 1]} ${year}`,
                year: +year,
                id: Number(`${year}${month}`)
              };
              this.prepareWorkExceptionsTreeRowData(i, tableHeader, workExceptions);
            }
          }
        }
        for (const totalProjection of compareprojections.totals_monthly) {
          const monthlyTotal = totalProjection.monthly_total;
          const effective_date_month = monthlyTotal?.month?.effective_date_1 === 0 ? monthlyTotal?.month?.effective_date_2 : monthlyTotal?.month?.effective_date_1;
          const effective_date_year = monthlyTotal?.year?.effective_date_1 === 0 ? monthlyTotal?.year?.effective_date_2 : monthlyTotal?.year?.effective_date_1;

          const month = effective_date_month.toString().length === 2 ? effective_date_month : '0' + effective_date_month;
          const tableHeader = {
            id: Number(`${effective_date_year}${month}`)
          };
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.revenueTreeData, 'revenue');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.pLAdjustData, 'revenue_plugs');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.sgaAdjustData, 'sga_plugs');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.netMargin, 'percent_net_profit');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.netProfit, 'net_profit');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.expenseTreeData, this.costConstant);
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.cOGSAdjust, 'expense_plugs');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.grossProfitTreeData, 'grossProfit');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.grossMarginTreeData, 'grossMargin');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.benchTreeData, 'unutilized_cost');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.projectExpenseTreeData, 'cost_less_unutilized');
          this.prepareCompareTotals(tableHeader, monthlyTotal, this.WorkExpenseTreeData, 'work_exception_cost');
        }
      }
      for (let i = 0; i < projection1?.projects?.length; i++) {
        this.fixedRevenueTreeData1[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.FIXED_REVENUE }
        };
        this.projectsRevenue1[i] = {
          data: { type: projection1.projects[i].project?.customer?.name, name: projection1.projects[i].project.name },
          children: []
        };
        this.projectsExpense1[i] = {
          data: { type: projection1.projects[i].project?.customer?.name, name: projection1.projects[i].project.name },
          children: []
        };
        this.tAndMRevenueTreeData1[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.T_M_REVENUE },
          children: []
        };
        this.peopleExpenseTreeData1[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.PEOPLE_EXPENSE },
          children: []
        };
        this.grossProfitData1[i] = {
          data: { type: projection1.projects[i].project?.customer?.name, name: projection1.projects[i].project.name }
        };

        this.grossMarginData1[i] = {
          data: { type: projection1.projects[i].project?.customer?.name, name: projection1.projects[i].project.name }
        };

        for (const validMonthlyProjection of projection1?.projects[i]?.project?.projection?.valid_monthly_projections) {
          const monthlyProjection = validMonthlyProjection?.valid_monthly_projection;
          if (monthlyProjection) {
            const month = monthlyProjection.month.toString().length === 2 ? monthlyProjection.month : '0' + monthlyProjection.month;
            const tableHeader: TableHeader = {
              month: monthlyProjection.month,
              monthLabel: `${MONTH_NAMES[monthlyProjection.month - 1]} ${monthlyProjection.year}`,
              year: monthlyProjection.year,
              id: Number(`${monthlyProjection.year}${month}`)
            };

            //add header only if month not already exist and maintain sort order too
            if (!this.tableHeaders.find((header) => header.id === tableHeader.id)) {
              this.tableHeaders = [...this.tableHeaders, tableHeader];
            }

            this.prepareTreeRowData1(i, tableHeader, monthlyProjection);
          }
        }
        for (let i = 0; i < projection1?.utilizations?.length; i++) {
          this.bench1[i] = {
            data: {
              type: `${projection1?.utilizations[i]?.employee?.first_name} ${projection1?.utilizations[i]?.employee?.last_name || ''}`,
              id: `${projection1?.utilizations[i]?.employee?.id}`
            },
            children: []
          };
          for (const utilization of projection1?.utilizations[i]?.employee?.utilizations) {
            const monthlyUtilization = utilization?.utilization;
            if (monthlyUtilization) {
              const month = monthlyUtilization.month.toString().length === 2 ? monthlyUtilization.month : '0' + monthlyUtilization.month;
              const tableHeader: TableHeader = {
                month: monthlyUtilization.month,
                monthLabel: `${MONTH_NAMES[monthlyUtilization.month - 1]} ${monthlyUtilization.year}`,
                year: monthlyUtilization.year,
                id: Number(`${monthlyUtilization.year}${month}`)
              };

              this.prepareBenchTreeRowData1(i, tableHeader, monthlyUtilization);
            }
          }
        }

        for (let i = 0; i < projection1.employees_plus_work_exceptions.length; i++) {
          this.workExceptions1[i] = {
            data: {
              type: `${projection1?.employees_plus_work_exceptions[i]?.employee?.first_name} ${projection1?.employees_plus_work_exceptions[i]?.employee?.last_name || ''}`,
              id: `${projection1?.employees_plus_work_exceptions[i]?.employee?.id}`
            },
            children: []
          };
          for (const workExceptions of projection1?.employees_plus_work_exceptions[i]?.employee.work_exception_summary) {
            if (workExceptions) {
              const month = workExceptions.month > 9 ? workExceptions.month : `0` + workExceptions.month;
              const tableHeader: TableHeader = {
                month: month,
                monthLabel: `${MONTH_NAMES[month - 1]} ${workExceptions.year}`,
                year: workExceptions.year,
                id: Number(`${workExceptions.year}${month}`)
              };
              this.prepareWorkExceptionsTreeRowData1(i, tableHeader, workExceptions);
            }
          }
        }

        for (const totalProjection of projection1.totals_monthly) {
          const monthlyTotal = totalProjection.monthly_total;
          const month = monthlyTotal.month.toString().length === 2 ? monthlyTotal.month : '0' + monthlyTotal.month;
          const tableHeader = {
            id: Number(`${monthlyTotal.year}${month}`)
          };
          this.prepareTotals(tableHeader, monthlyTotal, this.revenueTreeData1, 'revenue');
          this.prepareTotals(tableHeader, monthlyTotal, this.pLAdjustData1, 'revenue_plugs');
          this.prepareTotals(tableHeader, monthlyTotal, this.sgaAdjustData1, 'sga_plugs');
          this.prepareTotals(tableHeader, monthlyTotal, this.netMargin1, 'percent_net_profit');
          this.prepareTotals(tableHeader, monthlyTotal, this.netProfit1, 'net_profit');
          this.prepareTotals(tableHeader, monthlyTotal, this.expenseTreeData1, this.costConstant);
          this.prepareTotals(tableHeader, monthlyTotal, this.cOGSAdjust1, 'expense_plugs');
          this.prepareTotals(tableHeader, monthlyTotal, this.grossProfitTreeData1, 'grossProfit');
          this.prepareTotals(tableHeader, monthlyTotal, this.grossMarginTreeData1, 'grossMargin');
          this.prepareTotals(tableHeader, monthlyTotal, this.benchTreeData1, 'unutilized_cost');
          this.prepareTotals(tableHeader, monthlyTotal, this.projectExpenseTreeData1, 'cost_less_unutilized');
          this.prepareTotals(tableHeader, monthlyTotal, this.WorkExpenseTreeData1, 'work_exception_cost');
        }
      }
      for (let i = 0; i < projection2?.projects?.length; i++) {
        this.fixedRevenueTreeData2[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.FIXED_REVENUE }
        };
        this.projectsRevenue2[i] = {
          data: { type: projection2.projects[i].project?.customer?.name, name: projection2.projects[i].project.name },
          children: []
        };
        this.projectsExpense2[i] = {
          data: { type: projection2.projects[i].project?.customer?.name, name: projection2.projects[i].project.name },
          children: []
        };
        this.tAndMRevenueTreeData2[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.T_M_REVENUE },
          children: []
        };
        this.peopleExpenseTreeData2[i] = {
          data: { type: FINANCIAL_REVIEW_TYPES.PEOPLE_EXPENSE },
          children: []
        };
        this.grossProfitData2[i] = {
          data: { type: projection2.projects[i].project?.customer?.name, name: projection2.projects[i].project.name }
        };

        this.grossMarginData2[i] = {
          data: { type: projection2.projects[i].project?.customer?.name, name: projection2.projects[i].project.name }
        };

        for (const validMonthlyProjection of projection2?.projects[i]?.project?.projection?.valid_monthly_projections) {
          const monthlyProjection = validMonthlyProjection?.valid_monthly_projection;
          if (monthlyProjection) {
            const month = monthlyProjection.month.toString().length === 2 ? monthlyProjection.month : '0' + monthlyProjection.month;
            const tableHeader: TableHeader = {
              month: monthlyProjection.month,
              monthLabel: `${MONTH_NAMES[monthlyProjection.month - 1]} ${monthlyProjection.year}`,
              year: monthlyProjection.year,
              id: Number(`${monthlyProjection.year}${month}`)
            };

            //add header only if month not already exist and maintain sort order too
            if (!this.tableHeaders.find((header) => header.id === tableHeader.id)) {
              this.tableHeaders = [...this.tableHeaders, tableHeader];
            }

            this.prepareTreeRowData2(i, tableHeader, monthlyProjection);
          }
        }
        for (let i = 0; i < projection2?.utilizations?.length; i++) {
          this.bench2[i] = {
            data: {
              type: `${projection2?.utilizations[i]?.employee?.first_name} ${projection2?.utilizations[i]?.employee?.last_name || ''}`,
              id: `${projection2?.utilizations[i]?.employee?.id}`
            },
            children: []
          };
          for (const utilization of projection2?.utilizations[i]?.employee?.utilizations) {
            const monthlyUtilization = utilization?.utilization;
            if (monthlyUtilization) {
              const month = monthlyUtilization.month.toString().length === 2 ? monthlyUtilization.month : '0' + monthlyUtilization.month;
              const tableHeader: TableHeader = {
                month: monthlyUtilization.month,
                monthLabel: `${MONTH_NAMES[monthlyUtilization.month - 1]} ${monthlyUtilization.year}`,
                year: monthlyUtilization.year,
                id: Number(`${monthlyUtilization.year}${month}`)
              };

              this.prepareBenchTreeRowData2(i, tableHeader, monthlyUtilization);
            }
          }
        }
        for (let i = 0; i < projection2.employees_plus_work_exceptions.length; i++) {
          this.workExceptions2[i] = {
            data: {
              type: `${projection2?.employees_plus_work_exceptions[i]?.employee?.first_name} ${projection2?.employees_plus_work_exceptions[i]?.employee?.last_name || ''}`,
              id: `${projection2?.employees_plus_work_exceptions[i]?.employee?.id}`
            },
            children: []
          };
          for (const workExceptions of projection2?.employees_plus_work_exceptions[i]?.employee.work_exception_summary) {
            if (workExceptions) {
              const month = workExceptions.month > 9 ? workExceptions.month : `0` + workExceptions.month;
              const tableHeader: TableHeader = {
                month: month,
                monthLabel: `${MONTH_NAMES[month - 1]} ${workExceptions.year}`,
                year: workExceptions.year,
                id: Number(`${workExceptions.year}${month}`)
              };
              this.prepareWorkExceptionsTreeRowData2(i, tableHeader, workExceptions);
            }
          }
        }

        for (const totalProjection of projection2.totals_monthly) {
          const monthlyTotal = totalProjection.monthly_total;
          const month = monthlyTotal.month.toString().length === 2 ? monthlyTotal.month : '0' + monthlyTotal.month;
          const tableHeader = {
            id: Number(`${monthlyTotal.year}${month}`)
          };
          this.prepareTotals(tableHeader, monthlyTotal, this.revenueTreeData2, 'revenue');
          this.prepareTotals(tableHeader, monthlyTotal, this.pLAdjustData2, 'revenue_plugs');
          this.prepareTotals(tableHeader, monthlyTotal, this.sgaAdjustData2, 'sga_plugs');
          this.prepareTotals(tableHeader, monthlyTotal, this.netMargin2, 'percent_net_profit');
          this.prepareTotals(tableHeader, monthlyTotal, this.netProfit2, 'net_profit');
          this.prepareTotals(tableHeader, monthlyTotal, this.expenseTreeData2, this.costConstant);
          this.prepareTotals(tableHeader, monthlyTotal, this.cOGSAdjust2, 'expense_plugs');
          this.prepareTotals(tableHeader, monthlyTotal, this.grossProfitTreeData2, 'grossProfit');
          this.prepareTotals(tableHeader, monthlyTotal, this.grossMarginTreeData2, 'grossMargin');
          this.prepareTotals(tableHeader, monthlyTotal, this.benchTreeData2, 'unutilized_cost');
          this.prepareTotals(tableHeader, monthlyTotal, this.projectExpenseTreeData2, 'cost_less_unutilized');
          this.prepareTotals(tableHeader, monthlyTotal, this.WorkExpenseTreeData2, 'work_exception_cost');
        }
      }
    }

    this.prepareFinalProjectionData();
    this.loading$.next(false);
    this.cdf.detectChanges();
  }

  prepareTotals(tableHeader, totalProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    if (valueFieldName === 'grossProfit') {
      parentTreeNode.data[tableHeader.id] = Number(totalProjection['revenue']) - Number(totalProjection[this.costConstant]);
    }
    if (valueFieldName === 'grossMargin') {
      if (Number(totalProjection['revenue'])) {
        parentTreeNode.data[tableHeader.id] = ((Number(totalProjection['revenue']) - Number(totalProjection[this.costConstant])) / Number(totalProjection['revenue'])) * 100;
      } else {
        parentTreeNode.data[tableHeader.id] = 0;
      }
    }
    if (valueFieldName === 'percent_net_profit') {
      parentTreeNode.data[tableHeader.id] = Number(totalProjection[valueFieldName]) * 100;
    }
    if (
      valueFieldName === 'revenue' ||
      valueFieldName === 'sga_plugs' ||
      valueFieldName === 'net_profit' ||
      valueFieldName === 'revenue_plugs' ||
      valueFieldName === this.costConstant ||
      valueFieldName === 'expense_plugs' ||
      valueFieldName === 'unutilized_cost' ||
      valueFieldName === 'cost_less_unutilized' ||
      valueFieldName === 'work_exception_cost'
    ) {
      parentTreeNode.data[tableHeader.id] = Number(totalProjection[valueFieldName]);
    }
  }

  prepareCompareTotals(tableHeader, totalProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    if (valueFieldName === 'grossMargin') {
      parentTreeNode.data[tableHeader.id] = {
        diff: Number(totalProjection['percent_gross_margin']['diff']) * 100,
        effective_date_1: Number(totalProjection['percent_gross_margin']['effective_date_1']) * 100,
        effective_date_2: Number(totalProjection['percent_gross_margin']['effective_date_2']) * 100
      };
    }
    if (valueFieldName === 'grossProfit') {
      parentTreeNode.data[tableHeader.id] = {
        diff: Number(totalProjection['revenue']['diff']) - Number(totalProjection[this.costConstant]['diff']),
        effective_date_1: Number(totalProjection['revenue']['effective_date_1']) - Number(totalProjection[this.costConstant]['effective_date_1']),
        effective_date_2: Number(totalProjection['revenue']['effective_date_2']) - Number(totalProjection[this.costConstant]['effective_date_2'])
      };
    }
    if (valueFieldName === 'percent_net_profit') {
      parentTreeNode.data[tableHeader.id] = {
        diff: Number(totalProjection[valueFieldName]['diff']) * 100,
        effective_date_1: Number(totalProjection[valueFieldName]['effective_date_1']) * 100,
        effective_date_2: Number(totalProjection[valueFieldName]['effective_date_2']) * 100
      };
    }

    if (
      valueFieldName === 'revenue' ||
      valueFieldName === 'sga_plugs' ||
      valueFieldName === 'net_profit' ||
      valueFieldName === 'revenue_plugs' ||
      valueFieldName === this.costConstant ||
      valueFieldName === 'expense_plugs' ||
      valueFieldName === 'unutilized_cost' ||
      valueFieldName === 'cost_less_unutilized' ||
      valueFieldName === 'work_exception_cost'
    ) {
      parentTreeNode.data[tableHeader.id] = totalProjection[valueFieldName];
    }
  }

  private prepareTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareRevenueTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareExpenseTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossProfitRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossMarginRowData(projectIndex, tableHeader, validMonthlyProjection);
  }
  private prepareTreeRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareRevenueTreeRowData1(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareExpenseTreeRowData1(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossProfitRowData1(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossMarginRowData1(projectIndex, tableHeader, validMonthlyProjection);
  }
  private prepareTreeRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareRevenueTreeRowData2(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareExpenseTreeRowData2(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossProfitRowData2(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareGrossMarginRowData2(projectIndex, tableHeader, validMonthlyProjection);
  }

  private prepareRevenueTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareTandMRevenueTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareFixedRevenueTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
  }
  private prepareRevenueTreeRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareTandMRevenueTreeRowData1(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareFixedRevenueTreeRowData1(projectIndex, tableHeader, validMonthlyProjection);
  }
  private prepareRevenueTreeRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareTandMRevenueTreeRowData2(projectIndex, tableHeader, validMonthlyProjection);
    this.prepareFixedRevenueTreeRowData2(projectIndex, tableHeader, validMonthlyProjection);
  }

  private prepareGrossProfitRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareCompareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossProfitData[projectIndex], 'grossProfit');
  }

  private prepareGrossMarginRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareCompareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossMarginData[projectIndex], 'grossMargin');
  }
  private prepareGrossProfitRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossProfitData2[projectIndex], 'grossProfit');
  }

  private prepareGrossMarginRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossMarginData2[projectIndex], 'grossMargin');
  }

  private prepareGrossProfitRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossProfitData1[projectIndex], 'grossProfit');
  }

  private prepareGrossMarginRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.grossMarginData1[projectIndex], 'grossMargin');
  }

  private prepareFixedRevenueTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareCompareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.fixedRevenueTreeData[projectIndex], 'revenue');
  }

  private prepareTandMRevenueTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareCompareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsRevenue[projectIndex], 'revenue');
  }
  private prepareFixedRevenueTreeRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.fixedRevenueTreeData2[projectIndex], 'revenue');
  }

  private prepareTandMRevenueTreeRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsRevenue2[projectIndex], 'revenue');
  }
  private prepareFixedRevenueTreeRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareNodeData(projectIndex, tableHeader, validMonthlyProjection, this.fixedRevenueTreeData1[projectIndex], 'revenue');
  }

  private prepareTandMRevenueTreeRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsRevenue1[projectIndex], 'revenue');
  }

  private prepareExpenseTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.preparePeopleExpenseTreeRowData(projectIndex, tableHeader, validMonthlyProjection);
  }

  private preparePeopleExpenseTreeRowData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareCompareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsExpense[projectIndex], 'cost');
  }
  private prepareExpenseTreeRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.preparePeopleExpenseTreeRowData2(projectIndex, tableHeader, validMonthlyProjection);
  }

  private preparePeopleExpenseTreeRowData2(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsExpense2[projectIndex], 'cost');
  }
  private prepareExpenseTreeRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.preparePeopleExpenseTreeRowData1(projectIndex, tableHeader, validMonthlyProjection);
  }

  private preparePeopleExpenseTreeRowData1(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection) {
    this.prepareChildNodeData(projectIndex, tableHeader, validMonthlyProjection, this.projectsExpense1[projectIndex], 'cost');
  }

  private prepareBenchTreeRowData(employeeIndex, tableHeader: TableHeader, monthlyUtilization) {
    this.bench[employeeIndex].data[tableHeader.id] = {
      diff: Number(monthlyUtilization['amount']['diff']),
      effective_date_1: Number(monthlyUtilization['amount']['effective_date_1']),
      effective_date_2: Number(monthlyUtilization['amount']['effective_date_2'])
    };
  }

  private prepareBenchTreeRowData1(employeeIndex, tableHeader: TableHeader, monthlyUtilization) {
    this.bench1[employeeIndex].data[tableHeader.id] = Number(monthlyUtilization['amount']);
  }

  private prepareBenchTreeRowData2(employeeIndex, tableHeader: TableHeader, monthlyUtilization) {
    this.bench2[employeeIndex].data[tableHeader.id] = Number(monthlyUtilization['amount']);
  }

  private prepareWorkExceptionsTreeRowData(employeeIndex, tableHeader: TableHeader, workExceptions) {
    this.workExceptions[employeeIndex].data[tableHeader.id] = {
      diff: Number(workExceptions.work_exception_cost.diff),
      effective_date_1: Number(workExceptions.work_exception_cost.effective_date_1),
      effective_date_2: Number(workExceptions.work_exception_cost.effective_date_2)
    };
  }

  private prepareWorkExceptionsTreeRowData1(employeeIndex, tableHeader: TableHeader, monthlyUtilization) {
    this.workExceptions1[employeeIndex].data[tableHeader.id] = Number(monthlyUtilization['work_exception_cost']);
  }

  private prepareWorkExceptionsTreeRowData2(employeeIndex, tableHeader: TableHeader, monthlyUtilization) {
    this.workExceptions2[employeeIndex].data[tableHeader.id] = Number(monthlyUtilization['work_exception_cost']);
  }

  private prepareNodeData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection[valueFieldName]);
    if (valueFieldName === 'grossProfit') {
      parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection['revenue']) - Number(validMonthlyProjection[this.costConstant]);
    }
    if (valueFieldName === 'grossMargin') {
      parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection['percent_gross_margin']) * 100;
    }
  }

  private prepareCompareNodeData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    if (valueFieldName === 'grossMargin') {
      parentTreeNode.data[tableHeader.id] = {
        diff: Number(validMonthlyProjection['percent_gross_margin']['diff']) * 100,
        effective_date_1: Number(validMonthlyProjection['percent_gross_margin']['effective_date_1']) * 100,
        effective_date_2: Number(validMonthlyProjection['percent_gross_margin']['effective_date_2']) * 100
        // "diff" : Number(validMonthlyProjection['revenue']['diff']) !== 0 ? (((Number(validMonthlyProjection['revenue']['diff']) - Number(validMonthlyProjection['cost']['diff']))/Number(validMonthlyProjection['revenue']['diff'])) * 100) : 0,
        // "effective_date_1" : Number(validMonthlyProjection['revenue']['effective_date_1']) !== 0 ? (((Number(validMonthlyProjection['revenue']['effective_date_1']) - Number(validMonthlyProjection['cost']['effective_date_1']))/Number(validMonthlyProjection['revenue']['effective_date_1'])) * 100) : 0,
        // "effective_date_2" : Number(validMonthlyProjection['revenue']['effective_date_2']) !== 0 ? (((Number(validMonthlyProjection['revenue']['effective_date_2']) - Number(validMonthlyProjection['cost']['effective_date_2']))/Number(validMonthlyProjection['revenue']['effective_date_2'])) * 100) : 0
      };
    } else if (valueFieldName === 'grossProfit') {
      parentTreeNode.data[tableHeader.id] = {
        diff: Number(validMonthlyProjection['revenue']['diff']) - Number(validMonthlyProjection[this.costConstant]['diff']),
        effective_date_1: Number(validMonthlyProjection['revenue']['effective_date_1']) - Number(validMonthlyProjection[this.costConstant]['effective_date_1']),
        effective_date_2: Number(validMonthlyProjection['revenue']['effective_date_2']) - Number(validMonthlyProjection[this.costConstant]['effective_date_2'])
      };
    }
  }

  private prepareChildNodeData(projectIndex, tableHeader: TableHeader, validMonthlyProjection: ValidMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    let projectExpenseRevenue = false;
    parentTreeNode.data[tableHeader.id] = Number(validMonthlyProjection[valueFieldName]);

    for (const [index, validMonthlyPosition] of validMonthlyProjection.validated_monthly_positions.entries()) {
      const validatedMonthlyPosition = validMonthlyPosition?.validated_monthly_position;
      if (validMonthlyPosition) {
        let peopleExpensePosition = {};
        const existingRevenuePositionIndex = parentTreeNode.children.findIndex((child) => child?.data?.id === validatedMonthlyPosition.position.id);

        if (existingRevenuePositionIndex > -1) {
          peopleExpensePosition = parentTreeNode.children[existingRevenuePositionIndex];
          peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
          parentTreeNode.children[existingRevenuePositionIndex] = peopleExpensePosition;
        } else {
          if (valueFieldName === 'revenue' && validatedMonthlyPosition.position?.name === 'project_expenses') {
            // projectExpenseRevenue = true; // commented this as we need to show position which got removed as well under the revenue
          }
          if (!projectExpenseRevenue) {
            peopleExpensePosition = {
              data: {
                id: validatedMonthlyPosition.position.id,
                type: this.getTheTypeName(validatedMonthlyPosition.position?.name),
                name: `${validatedMonthlyPosition.position?.employee ? validatedMonthlyPosition.position?.employee?.first_name : ''} ${
                  validatedMonthlyPosition.position?.employee
                    ? validatedMonthlyPosition.position?.employee?.last_name
                    : validatedMonthlyPosition?.position?.name !== 'project_expenses'
                    ? '-- Open --'
                    : '--- ---'
                }`
              }
            };
            peopleExpensePosition['data'][tableHeader.id] = Number(validatedMonthlyPosition[valueFieldName]);
            parentTreeNode.children = [...parentTreeNode.children, peopleExpensePosition];
            if (valueFieldName === this.costConstant) {
              const targetIndex = parentTreeNode.children.findIndex((obj) => obj.data.type === 'Project Expenses');

              if (targetIndex !== -1) {
                const targetObject = parentTreeNode.children.splice(targetIndex, 1)[0];
                parentTreeNode.children.push(targetObject);
              }
            }
          }
        }
      }
    }
  }
  private prepareCompareChildNodeData(projectIndex, tableHeader: TableHeader, validMonthlyProjection, parentTreeNode: TreeNode, valueFieldName: string) {
    let projectExpenseRevenue = false;
    parentTreeNode.data[tableHeader.id] = validMonthlyProjection[valueFieldName];

    for (const [index, validMonthlyPosition] of validMonthlyProjection.validated_monthly_positions.entries()) {
      const validatedMonthlyPosition = validMonthlyPosition?.validated_monthly_position;
      if (validMonthlyPosition) {
        let peopleExpensePosition = {};
        const existingRevenuePositionIndex = parentTreeNode.children.findIndex((child) => child?.data?.id === validatedMonthlyPosition?.position?.id?.diff);

        if (existingRevenuePositionIndex > -1) {
          peopleExpensePosition = parentTreeNode.children[existingRevenuePositionIndex];
          peopleExpensePosition['data'][tableHeader.id] = validatedMonthlyPosition[valueFieldName];
          parentTreeNode.children[existingRevenuePositionIndex] = peopleExpensePosition;
        } else {
          if (valueFieldName === 'revenue' && validatedMonthlyPosition?.position?.name?.diff === 'project_expenses') {
            // projectExpenseRevenue = true; // commented this as we need to show position which got removed as well under the revenue
          }
          if (!projectExpenseRevenue) {
            peopleExpensePosition = {
              data: {
                id: validatedMonthlyPosition?.position?.id?.diff,
                type: this.getTheTypeName(validatedMonthlyPosition?.position?.name?.diff),
                // type: validatedMonthlyPosition?.position?.name?.diff,
                // name: `${validatedMonthlyPosition?.position?.employee?.first_name?.diff || ""
                //   } ${validatedMonthlyPosition?.position?.employee?.last_name?.diff || ""}`,
                name: `${validatedMonthlyPosition?.position?.employee ? validatedMonthlyPosition?.position?.employee?.first_name?.diff : ''} ${
                  validatedMonthlyPosition?.position?.employee
                    ? validatedMonthlyPosition?.position?.employee?.last_name?.diff
                    : validatedMonthlyPosition?.position?.name?.diff !== 'project_expenses'
                    ? '-- Open --'
                    : '--- ---'
                }`
              }
            };
            peopleExpensePosition['data'][tableHeader.id] = validatedMonthlyPosition[valueFieldName];
            parentTreeNode.children = [...parentTreeNode.children, peopleExpensePosition];

            if (valueFieldName === this.costConstant) {
              const targetIndex = parentTreeNode.children.findIndex((obj) => obj.data.type === 'Project Expenses');

              if (targetIndex !== -1) {
                const targetObject = parentTreeNode.children.splice(targetIndex, 1)[0];
                parentTreeNode.children.push(targetObject);
              }
            }
          }
        }
      }
    }
  }

  private getTheTypeName(positionNameDiff: string): string {
    if (positionNameDiff?.includes('_')) {
      positionNameDiff = positionNameDiff.replace('_', ' ');
      positionNameDiff = positionNameDiff.replace(/\w\S*/g, (txt) => {
        return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();
      });
    }
    return positionNameDiff;
  }

  private addZeroToNonAvailableData(projectionData) {
    for (const treeNodeRowData of projectionData) {
      this.addNotFoundHeaderKeyValue(treeNodeRowData.data);
      const rowDataChildren = treeNodeRowData.children;
      this.addZeroToTreeChild(rowDataChildren);
    }
  }

  private addZeroToTreeChild(children) {
    while (children && children.length) {
      for (const rowDataChild of children) {
        this.addNotFoundHeaderKeyValue(rowDataChild.data);
        children = rowDataChild.children;
        if (children) {
          this.addZeroToTreeChild(children);
        }
      }
    }
  }

  private addNotFoundHeaderKeyValue(data) {
    if (data) {
      let rowDataKeys: any[] = Object.keys(data);
      rowDataKeys = rowDataKeys.map((key) => Number(key));
      const notFoundHeaderKeys = this.tableHeaders.filter((tableHeader) => !rowDataKeys.includes(tableHeader.id)).map((tableHeader) => tableHeader.id);
      if (notFoundHeaderKeys && notFoundHeaderKeys.length) {
        for (const notFoundHeaderKey of notFoundHeaderKeys) {
          data[notFoundHeaderKey] = 0;
        }
      }
    }
  }

  private addZeroToNonAvailableDataCompare(projectionData) {
    for (const treeNodeRowData of projectionData) {
      this.addNotFoundHeaderKeyValueCompare(treeNodeRowData.data);
      const rowDataChildren = treeNodeRowData.children;
      this.addZeroToTreeChildCompare(rowDataChildren);
    }
  }

  private addZeroToTreeChildCompare(children) {
    while (children && children.length) {
      for (const rowDataChild of children) {
        this.addNotFoundHeaderKeyValueCompare(rowDataChild.data);
        children = rowDataChild.children;
        if (children) {
          this.addZeroToTreeChildCompare(children);
        }
      }
    }
  }

  private addNotFoundHeaderKeyValueCompare(data) {
    if (data) {
      let rowDataKeys: any[] = Object.keys(data);
      rowDataKeys = rowDataKeys.map((key) => Number(key));
      const notFoundHeaderKeys = this.tableHeaders.filter((tableHeader) => !rowDataKeys.includes(tableHeader.id)).map((tableHeader) => tableHeader.id);
      if (notFoundHeaderKeys && notFoundHeaderKeys.length) {
        for (const notFoundHeaderKey of notFoundHeaderKeys) {
          data[notFoundHeaderKey] = {
            diff: '0.00',
            effective_date_1: '0.00',
            effective_date_2: '0.00'
          };
        }
      }
    }
  }

  prepareFinalProjectionData() {
    this.finalProjectionData = [];
    this.projectionData1 = [];
    this.projectionData2 = [];
    this.finalProjectionData = [
      this.getRevenueTree(),
      this.getExpenseTree(),
      this.getGrossProfitData(),
      this.getGrossMarginData(),
      this.sgaAdjustData,
      this.netProfit,
      this.netMargin
    ];
    this.projectionData1 = [
      this.getRevenueTree1(),
      this.getExpenseTree1(),
      this.getGrossProfitData1(),
      this.getGrossMarginData1(),
      this.sgaAdjustData1,
      this.netProfit1,
      this.netMargin1
    ];
    this.projectionData2 = [
      this.getRevenueTree2(),
      this.getExpenseTree2(),
      this.getGrossProfitData2(),
      this.getGrossMarginData2(),
      this.sgaAdjustData2,
      this.netProfit2,
      this.netMargin2
    ];
    this.layoutConfigService.updateHeight$.next(true);
    this.height = 'calc((var(--fixed-content-height, 1vh) * 100) - 160px)';
    this.addZeroToNonAvailableDataCompare(this.finalProjectionData);
    this.addZeroToNonAvailableData(this.projectionData1);
    this.addZeroToNonAvailableData(this.projectionData2);
    this.makeRowsSameHeight();
    this.cdf.detectChanges();
  }

  getGrossProfitData() {
    this.grossProfitTreeData = {
      ...this.grossProfitTreeData,
      children: this.grossProfitData
    };
    return this.grossProfitTreeData;
  }

  getGrossProfitData1() {
    this.grossProfitTreeData1 = {
      ...this.grossProfitTreeData1,
      children: this.grossProfitData1
    };
    return this.grossProfitTreeData1;
  }
  getGrossProfitData2() {
    this.grossProfitTreeData2 = {
      ...this.grossProfitTreeData2,
      children: this.grossProfitData2
    };
    return this.grossProfitTreeData2;
  }

  getGrossMarginData() {
    this.grossMarginTreeData = {
      ...this.grossMarginTreeData,
      children: this.grossMarginData
    };
    return this.grossMarginTreeData;
  }
  getGrossMarginData1() {
    this.grossMarginTreeData1 = {
      ...this.grossMarginTreeData1,
      children: this.grossMarginData1
    };
    return this.grossMarginTreeData1;
  }
  getGrossMarginData2() {
    this.grossMarginTreeData2 = {
      ...this.grossMarginTreeData2,
      children: this.grossMarginData2
    };
    return this.grossMarginTreeData2;
  }

  getExpenseTree(): TreeNode {
    this.expenseTreeData = {
      ...this.expenseTreeData,
      children: [this.getBenchTree(), this.getProjectExpenseTree(), this.getWorkExceptionTree(), this.cOGSAdjust]
    };
    return this.expenseTreeData;
  }

  getExpenseTree1(): TreeNode {
    this.expenseTreeData1 = {
      ...this.expenseTreeData1,
      children: [this.getBenchTree1(), this.getProjectExpenseTree1(), this.getWorkExceptionTree1(), this.cOGSAdjust1]
    };
    return this.expenseTreeData1;
  }
  getExpenseTree2(): TreeNode {
    this.expenseTreeData2 = {
      ...this.expenseTreeData2,
      children: [this.getBenchTree2(), this.getProjectExpenseTree2(), this.getWorkExceptionTree2(), this.cOGSAdjust2]
    };
    return this.expenseTreeData2;
  }

  getBenchTree(): TreeNode {
    this.benchTreeData = {
      ...this.benchTreeData,
      children: this.getBench()
    };
    return this.benchTreeData;
  }
  getBenchTree1(): TreeNode {
    this.benchTreeData1 = {
      ...this.benchTreeData1,
      children: this.getBench1()
    };
    return this.benchTreeData1;
  }
  getBenchTree2(): TreeNode {
    this.benchTreeData2 = {
      ...this.benchTreeData2,
      children: this.getBench2()
    };
    return this.benchTreeData2;
  }

  getWorkExceptionTree(): TreeNode {
    this.WorkExpenseTreeData = { ...this.WorkExpenseTreeData, children: this.getWorkException() };
    return this.WorkExpenseTreeData;
  }

  getWorkExceptionTree1(): TreeNode {
    this.WorkExpenseTreeData1 = { ...this.WorkExpenseTreeData1, children: this.getWorkException1() };
    return this.WorkExpenseTreeData1;
  }

  getWorkExceptionTree2(): TreeNode {
    this.WorkExpenseTreeData2 = { ...this.WorkExpenseTreeData2, children: this.getWorkException2() };
    return this.WorkExpenseTreeData2;
  }

  getProjectExpenseTree(): TreeNode {
    this.projectExpenseTreeData = {
      ...this.projectExpenseTreeData,
      children: this.getProjectExpense()
    };
    return this.projectExpenseTreeData;
  }
  getProjectExpenseTree1(): TreeNode {
    this.projectExpenseTreeData1 = {
      ...this.projectExpenseTreeData1,
      children: this.getProjectExpense1()
    };
    return this.projectExpenseTreeData1;
  }
  getProjectExpenseTree2(): TreeNode {
    this.projectExpenseTreeData2 = {
      ...this.projectExpenseTreeData2,
      children: this.getProjectExpense2()
    };
    return this.projectExpenseTreeData2;
  }

  getRevenueTree(): TreeNode {
    this.revenueTreeData = {
      ...this.revenueTreeData,
      children: [...this.getProjectRevenue(), this.getPLAdjustData()]
    };
    return this.revenueTreeData;
  }
  getRevenueTree1(): TreeNode {
    this.revenueTreeData1 = {
      ...this.revenueTreeData1,
      children: [...this.getProjectRevenue1(), this.getPLAdjustData1()]
    };
    return this.revenueTreeData1;
  }
  getRevenueTree2(): TreeNode {
    this.revenueTreeData2 = {
      ...this.revenueTreeData2,
      children: [...this.getProjectRevenue2(), this.getPLAdjustData2()]
    };
    return this.revenueTreeData2;
  }

  getPLAdjustData() {
    return this.pLAdjustData;
  }
  getPLAdjustData1() {
    return this.pLAdjustData1;
  }
  getPLAdjustData2() {
    return this.pLAdjustData2;
  }

  getProjectRevenue() {
    for (const projectrevenue of this.projectsRevenue) {
      projectrevenue.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsRevenue;
  }
  getProjectRevenue1() {
    for (const projectrevenue of this.projectsRevenue1) {
      projectrevenue.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsRevenue1;
  }
  getProjectRevenue2() {
    for (const projectrevenue of this.projectsRevenue2) {
      projectrevenue.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsRevenue2;
  }

  getProjectExpense() {
    for (const projectexpense of this.projectsExpense) {
      projectexpense.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsExpense;
  }
  getProjectExpense1() {
    for (const projectexpense of this.projectsExpense1) {
      projectexpense.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsExpense1;
  }
  getProjectExpense2() {
    for (const projectexpense of this.projectsExpense2) {
      projectexpense.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.projectsExpense2;
  }

  getBench() {
    for (const bench of this.bench) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.bench;
  }

  getBench1() {
    for (const bench of this.bench1) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.bench1;
  }
  getBench2() {
    for (const bench of this.bench2) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.bench2;
  }

  getWorkException() {
    for (const bench of this.workExceptions) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.workExceptions;
  }

  getWorkException1() {
    for (const bench of this.workExceptions1) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.workExceptions1;
  }

  getWorkException2() {
    for (const bench of this.workExceptions2) {
      bench.children.forEach((child) => {
        delete child.data.id;
      });
    }
    return this.workExceptions2;
  }

  getIsProjectTandM(projectIndex): boolean {
    return this.projections?.projects[projectIndex]?.project.billing_type === BillingTypes.TIME_MATERIALS;
  }

  getStyle(rowNode) {
    if (rowNode?.level === 0) {
      return 'font-weight-bold';
    }
    if (rowNode?.level === 1) {
      return 'children-level-1';
    }
  }

  getMonthlyValues(rowData: TreeNode, rowNode) {
    if (rowData) {
      const montlyData = JSON.parse(JSON.stringify(rowData));
      const typeExcluded = this.omit('type', montlyData);
      const idExcluded = this.omit('id', typeExcluded);
      const nanExcluded = this.omit('NaN', idExcluded);
      return this.omit('name', nanExcluded);
    }
    return;
  }

  private omit(key, obj) {
    const { [key]: omitted, ...rest } = obj;
    return rest;
  }

  preserveOriginalOrder = (a: KeyValue<number, string>, b: KeyValue<number, string>): number => {
    return 0;
  };

  getTotalVariance(rowData: TreeNode, rowNode?, index?) {
    let projection = this.projections.projections[2].projections_diff;
    switch (index) {
      case 0:
        projection = this.projections.projections[2].projections_diff;
        break;
      case 1:
        projection = this.projections.projections[0].projection1;
        break;
      case 2:
        projection = this.projections.projections[1].projection2;
        break;
    }

    if (index === 0 && rowData?.type) {
      const profit = Number(projection?.total?.revenue?.diff) - Number(projection?.total?.cost?.diff);
      switch (rowData.type) {
        case FINANCIAL_REVIEW_TYPES.GROSS_MARGIN:
          if (Number(projection?.total?.revenue?.diff) === 0) {
            return 0;
          }
          return (profit / Number(projection?.total?.revenue?.diff)) * 100;
        case FINANCIAL_REVIEW_TYPES.REVENUE:
          return projection.total?.revenue?.diff;
        case FINANCIAL_REVIEW_TYPES.EXPENSE:
          return projection.total?.cost?.diff;
        case FINANCIAL_REVIEW_TYPES.GROSS_PROFIT:
          return profit;
        case FINANCIAL_REVIEW_TYPES.BENCH:
          return projection?.total?.unutilized_cost?.diff;
        case FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION:
          return projection?.total?.work_exception_cost.diff;
        case FINANCIAL_REVIEW_TYPES.PROJECTS:
          return projection?.total?.cost_less_unutilized?.diff;
        case FINANCIAL_REVIEW_TYPES.PL_ADJUST:
          return projection?.total?.revenue_plugs?.diff;
        case FINANCIAL_REVIEW_TYPES.COGS_ADJUST:
          return projection?.total?.expense_plugs?.diff;
        case FINANCIAL_REVIEW_TYPES.SGA_ADJUST:
          return projection?.total?.sga_plugs?.diff;
        case FINANCIAL_REVIEW_TYPES.NET_PROFIT:
          return projection?.total?.net_profit?.diff;
        case FINANCIAL_REVIEW_TYPES.NET_MARGIN:
          return projection?.total?.percent_net_profit?.diff * 100;
        default:
          if (rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.BENCH || rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION) {
            let abc = 0;
            for (const totalProjection of projection.totals_monthly) {
              const monthlyTotal = totalProjection.monthly_total;
              const month = monthlyTotal.month['diff'].toString().length === 2 ? monthlyTotal.month['diff'] : '0' + monthlyTotal.month['diff'];
              const temp = rowData[Number(`${monthlyTotal.year['diff']}${month}`)];
              if (temp['diff']) {
                abc += Number(temp['diff']);
              }
            }
            return abc;
          } else if (rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN) {
            return;
          }
      }
    } else {
      if (rowData?.type) {
        const profit = Number(projection?.total?.revenue) - Number(projection?.total?.cost);

        switch (rowData.type) {
          case FINANCIAL_REVIEW_TYPES.GROSS_MARGIN:
            if (Number(projection?.total?.revenue) === 0) {
              return 0;
            }
            return (profit / Number(projection?.total?.revenue)) * 100;
          case FINANCIAL_REVIEW_TYPES.REVENUE:
            return projection?.total?.revenue || 0;
          case FINANCIAL_REVIEW_TYPES.EXPENSE:
            return projection.total?.cost;
          case FINANCIAL_REVIEW_TYPES.GROSS_PROFIT:
            return profit;
          case FINANCIAL_REVIEW_TYPES.BENCH:
            return projection?.total?.unutilized_cost;
          case FINANCIAL_REVIEW_TYPES.PROJECTS:
            return projection?.total?.cost_less_unutilized;
          case FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION:
            return projection?.total?.work_exception_cost;
          case FINANCIAL_REVIEW_TYPES.PL_ADJUST:
            return projection?.total?.revenue_plugs;
          case FINANCIAL_REVIEW_TYPES.COGS_ADJUST:
            return projection?.total?.expense_plugs;
          case FINANCIAL_REVIEW_TYPES.SGA_ADJUST:
            return projection?.total?.sga_plugs;
          case FINANCIAL_REVIEW_TYPES.NET_PROFIT:
            return projection?.total?.net_profit;
          case FINANCIAL_REVIEW_TYPES.NET_MARGIN:
            return projection?.total?.percent_net_profit * 100;
        }
      }
      let total;
      if (rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN) {
        for (let i = 0; i < projection?.projects?.length; i++) {
          if (projection.projects[i].project.name === rowData.type) {
            return projection.projects[i].project.projection.percent_gross_margin * 100;
          }
        }
      }
    }
  }

  getTotal(rowData: TreeNode, rowNode?, index?) {
    if (this.projections?.projections?.length) {
      let projection;
      switch (index) {
        case 0:
          projection = this.projections.projections[2].projections_diff;
          break;
        case 1:
          projection = this.projections.projections[0].projection1;
          break;
        case 2:
          projection = this.projections.projections[1].projection2;
          break;
      }
      if (index === 0) {
        if (rowData?.type) {
          const profit = Number(projection?.total?.revenue?.effective_date_2) - Number(projection?.total?.cost?.effective_date_2);
          switch (rowData.type) {
            case FINANCIAL_REVIEW_TYPES.GROSS_MARGIN:
              if (Number(projection?.total?.revenue?.effective_date_2) === 0) {
                return 0;
              }
              return (profit / Number(projection?.total?.revenue?.effective_date_2)) * 100;
            case FINANCIAL_REVIEW_TYPES.REVENUE:
              return projection?.total?.revenue?.effective_date_2 || 0;
            case FINANCIAL_REVIEW_TYPES.EXPENSE:
              return projection.total?.cost?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.GROSS_PROFIT:
              return profit;
            case FINANCIAL_REVIEW_TYPES.BENCH:
              return projection?.total?.unutilized_cost?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION:
              return projection?.total?.work_exception_cost.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.PROJECTS:
              return projection?.total?.cost_less_unutilized?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.PL_ADJUST:
              return projection?.total?.revenue_plugs?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.COGS_ADJUST:
              return projection?.total?.expense_plugs?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.SGA_ADJUST:
              return projection?.total?.sga_plugs?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.NET_PROFIT:
              return projection?.total?.net_profit?.effective_date_2;
            case FINANCIAL_REVIEW_TYPES.NET_MARGIN:
              return projection?.total?.percent_net_profit?.effective_date_2 * 100;
          }
        }

        let total;

        if (rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN) {
          for (let i = 0; i < projection?.projects?.length; i++) {
            if (projection.projects[i].project.name.diff === rowData.type) {
              return Number(projection.projects[i].project.projection.percent_gross_margin.effective_date_2) * 100;
            }
          }
        } else {
          const rowDataWithoutType = this.omit('type', rowData);
          const rowDataWithoutName = this.omit('name', rowDataWithoutType);
          const rowDataWithoutId = this.omit('id', rowDataWithoutName);
          const nanExcluded = this.omit('NaN', rowDataWithoutId);
          total = this.compareSumValues(nanExcluded);
        }
        return total;
      } else {
        if (rowData?.type) {
          const profit = Number(projection?.total?.revenue) - Number(projection?.total?.cost);

          switch (rowData.type) {
            case FINANCIAL_REVIEW_TYPES.GROSS_MARGIN:
              if (Number(projection?.total?.revenue) === 0) {
                return 0;
              }
              return (profit / Number(projection?.total?.revenue)) * 100;
            case FINANCIAL_REVIEW_TYPES.REVENUE:
              return projection.total?.revenue;
            case FINANCIAL_REVIEW_TYPES.EXPENSE:
              return projection.total?.cost;
            case FINANCIAL_REVIEW_TYPES.GROSS_PROFIT:
              return profit;
            case FINANCIAL_REVIEW_TYPES.BENCH:
              return projection?.total?.unutilized_cost;
            case FINANCIAL_REVIEW_TYPES.PROJECTS:
              return projection?.total?.cost_less_unutilized;
            case FINANCIAL_REVIEW_TYPES.WORK_EXCEPTION:
              return projection?.total?.work_exception_cost;
            case FINANCIAL_REVIEW_TYPES.PL_ADJUST:
              return projection?.total?.revenue_plugs;
            case FINANCIAL_REVIEW_TYPES.COGS_ADJUST:
              return projection?.total?.expense_plugs;
            case FINANCIAL_REVIEW_TYPES.SGA_ADJUST:
              return projection?.total?.sga_plugs;
            case FINANCIAL_REVIEW_TYPES.NET_PROFIT:
              return projection?.total?.net_profit;
            case FINANCIAL_REVIEW_TYPES.NET_MARGIN:
              return projection?.total?.percent_net_profit * 100;
          }
        }
        let total;
        if (rowNode?.node?.parent?.data?.type === FINANCIAL_REVIEW_TYPES.GROSS_MARGIN) {
          for (let i = 0; i < projection?.projects?.length; i++) {
            if (projection.projects[i].project.name === rowData.type) {
              return projection.projects[i].project.projection.percent_gross_margin * 100;
            }
          }
        } else {
          const rowDataWithoutType = this.omit('type', rowData);
          const rowDataWithoutName = this.omit('name', rowDataWithoutType);
          const rowDataWithoutId = this.omit('id', rowDataWithoutName);
          const nanExcluded = this.omit('NaN', rowDataWithoutId);
          total = this.sumValues(nanExcluded);
        }
        return total;
      }
    } else {
      return 0;
    }
  }

  sumValues = (obj) =>
    Object.values(obj).reduce((a: any, b: any) => {
      return a + b;
    }, 0);

  compareSumValues = (obj) =>
    Object.values(obj).reduce((a: any, b: any) => {
      return a + Number(b.effective_date_2);
    }, 0);

  openSideBar() {
    this.sidebarParams = { template: this.el };
    this.openFilter = true;
  }

  applyTags() {
    if (!this.dataFilter.effective_date2) {
      this.dataFilter.effective_date2 = new Date();
    }

    this.tags = [];
    if (!this.dataFilter) {
      return;
    }
    if (this.dataFilter.effective_date1) {
      this.dataFilter.effective_date1 = new Date(decodeURIComponent(this.dataFilter?.effective_date1));
      if (this.dataFilter?.effective_date2) {
        this.dataFilter.effective_date2 = new Date(decodeURIComponent(this.dataFilter?.effective_date2));
      }
      if (!this.dataFilter.effective_date1) {
        this.tags.push({
          label: 'Historical Data',
          value: this.datePipe.transform(this.dataFilter.effective_date1, 'MM/dd/yyyy, hh:mm a'),
          key: ['dataType1', 'effective_date1', 'dataType2', 'effective_date2']
        });
      }
    }
    if (this.dataFilter.rollingOption) {
      this.tags.push({
        label: 'Rolling',
        value: this.dataFilter.rollingOption.toString(),
        key: ['start_date', 'end_date', 'rollingOption']
      });
    }
    if (this.dataFilter.quarter) {
      ``;
      this.tags.push({
        label: 'By Quarter',
        value: this.dataFilter.quarter,
        key: ['period', 'quarter', 'start_date', 'end_date']
      });
    }
    if (this.dataFilter.year) {
      this.tags.push({
        label: 'By Year',
        value: this.dataFilter.year.toString(),
        key: ['period', 'year', 'start_date', 'end_date']
      });
    }
    if (this.dataFilter.start_date && this.dataFilter.end_date && !this.dataFilter.rollingOption && !this.dataFilter.year && !this.dataFilter.quarter) {
      const dateFormat = 'MM/dd/yyyy';
      const value = this.datePipe.transform(this.dataFilter.start_date, dateFormat) + ' - ' + this.datePipe.transform(this.dataFilter.end_date, dateFormat);
      this.tags.push({
        label: 'Date Range',
        value: value,
        key: ['period', 'start_date', 'end_date', 'start_month', 'end_month']
      });
    }

    if (this.dataFilter.project_name) {
      this.tags.push({
        label: 'Project Group',
        value: this.dataFilter.project_name.name,
        key: ['project_name', 'project_ids']
      });
    }
    if (this.dataFilter.include_utilizations !== undefined) {
      this.tags.push({
        label: 'Include Bench',
        value: this.dataFilter.include_utilizations ? 'True' : 'False',
        key: ['include_utilizations']
      });
    }
    if (this.dataFilter.include_pl_plugs !== undefined) {
      this.tags.push({
        label: 'Include P&L Plugs',
        value: this.dataFilter.include_pl_plugs ? 'True' : 'False',
        key: ['include_pl_plugs']
      });
    }

    if (this.dataFilter.customer_name) {
      this.tags.push({
        label: 'Client Group',
        value: this.dataFilter.customer_name.name,
        key: ['customer_name', 'customer_ids']
      });
    }
    if (this.dataFilter.client) {
      this.tags.push({
        label: 'Client',
        value: this.dataFilter.client.name,
        key: ['client']
      });
    }

    if (this.dataFilter?.ClientName?.length) {
      this.tags.push({
        label: 'Client',
        value: this.dataFilter?.ClientName?.join(','),
        key: ['ClientName', 'customer_ids', 'selectedClient']
      });
    }
    if (this.dataFilter?.projectName?.length) {
      this.tags.push({
        label: 'Project',
        value: this.dataFilter?.projectName?.join(','),
        key: ['projectName', 'project_ids', 'selectedProject']
      });
    }

    if (this.dataFilter.statuses) {
      this.tags.push({
        label: 'Project Status',
        value: this.dataFilter.statuses
      });
    }
  }

  getGrossMarginTotal(data) {
    const project = this.projections?.projects?.filter((projects) => projects?.project?.name === data.type)[0];
    return Number(project?.project?.projection?.percent_gross_margin) * 100;
  }

  exportReport(type) {
    const fileName = `P&L_Report_${moment().format('YYYY')}_${moment().format('MM')}_${moment().format('DD')}_${moment().format('HH')}${moment().format('mm')}`;
    const colNames = {
      project: '1~project',
      type: '2~type',
      subType: '3~subType',
      name: '4~name',
      monthPrefix: 'm~',
      total: 'z~total'
    };

    if (type === 'csv') {
      Object.keys(colNames).forEach((col) => {
        colNames[col] = colNames[col].split('~')[1];
      });
    }

    const csvHeaders = [
      colNames.project,
      colNames.type,
      colNames.subType,
      colNames.name,
      ...this.tableHeaders.map((header) => colNames.monthPrefix + header.id.toString()),
      colNames.total
    ];

    const pdfHeader = [
      {
        title: 'P&L Category',
        dataKey: colNames.project
      },
      {
        title: 'Project',
        dataKey: colNames.type
      },
      {
        title: 'Position',
        dataKey: colNames.subType
      },
      {
        title: 'Name',
        dataKey: colNames.name
      },
      ...this.tableHeaders.map((header) => ({
        title: header.monthLabel,
        dataKey: colNames.monthPrefix + header.id.toString()
      })),
      {
        title: 'Total',
        dataKey: colNames.total
      }
    ];

    const excelHeader = {
      [colNames.project]: 'P&L Category',
      [colNames.type]: 'Project',
      [colNames.subType]: 'Position',
      [colNames.name]: 'Employee'
    };
    this.tableHeaders.forEach((header) => {
      excelHeader[colNames.monthPrefix + header.id.toString()] = header.monthLabel + '(in $)';
    });
    excelHeader[colNames.total] = 'Total (in $)';

    const exportReportData = [];
    const doAddChildData = (data = [], level = 1, parent = '') => {
      let parentNode = parent;
      for (let i = 0; i < data.length; i++) {
        const rowData = {
          ...data[i].data,
          [colNames.project]: '',
          [colNames.type]: data[i].data.type || '',
          [colNames.subType]: '',
          [colNames.name]: data[i].data.name || ''
        };
        if (level === 3 || level === 4) {
          rowData[colNames.subType] = rowData[colNames.type];
          rowData[colNames.type] = '';
        }
        if (parentNode === AppConstants.grossMargin) {
          rowData[colNames.total] = this.getGrossMarginTotal(data[i].data);
        } else {
          rowData[colNames.total] = Math.round(this.getTotal(data[i].data));
        }

        if (level === 1) {
          //project level
          rowData[colNames.project] = rowData[colNames.type];
          rowData[colNames.type] = '';
          parentNode = rowData[colNames.project];

          //removing zeros from project level
          Object.keys(rowData).forEach((prop) => {
            if (rowData[prop] === 0) {
              rowData[prop] = '';
            }
          });
        }

        //change object property names to maintain order to prevent column order issue in excel and csv
        if (type === 'pdf') {
          Object.keys(rowData).forEach((prop) => {
            //month column will be in number and we need to prefix it
            if (!isNaN(Number(prop))) {
              if (parentNode === AppConstants.grossMargin) {
                rowData[colNames.monthPrefix + prop] =
                  Math.round(rowData[prop])
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
              } else {
                rowData[colNames.monthPrefix + prop] =
                  '$' +
                  Math.round(rowData[prop])
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              }

              delete rowData[prop];
            }
          });
          if (parentNode === AppConstants.grossMargin) {
            rowData[colNames.total] = rowData[colNames.total]?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
          } else {
            rowData[colNames.total] =
              '$' +
              Math.round(rowData[colNames.total])
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }

          //removing not required fileds
          delete rowData.type;
          delete rowData.name;
        }
        if (type === 'excel') {
          Object.keys(rowData).forEach((prop) => {
            //month column will be in number and we need to prefix it
            if (!isNaN(Number(prop))) {
              rowData[colNames.monthPrefix + prop] = Math.round(rowData[prop]);
              delete rowData[prop];
            }
          });

          //removing not required fileds
          delete rowData.type;
          delete rowData.name;
        }

        exportReportData.push(rowData);

        if (data[i].children?.length > 0) {
          doAddChildData(data[i].children, level + 1, data[i].data.type);
        }
      }
    };
    doAddChildData(this.finalProjectionData);

    if (type === 'csv') {
      this.utilizationService.exportToCsv(exportReportData, fileName, csvHeaders);
    }
    if (type === 'pdf') {
      this.utilizationService.exportPdf(pdfHeader, exportReportData, fileName, 4);
    }
    if (type === 'excel') {
      this.utilizationService.exportExcel([excelHeader], exportReportData, fileName);
    }
  }

  onCancelFilter(tagValue) {
    tagValue?.key?.forEach((key) => {
      if (key === 'include_utilizations' || key === 'include_pl_plugs') {
        this.dataFilter[key] = false;
      } else this.dataFilter[key] = null;
    });

    this.doFilterData();
  }

  onRemoveStatusFilter(statusValue) {
    if (statusValue.length) {
      this.dataFilter.statuses = statusValue.join(',');
      this.dataFilter.status = statusValue;
    } else {
      this.dataFilter.statuses = null;
      this.dataFilter.status = null;
    }
    this.doFilterData();
  }

  doFilterData() {
    this.dateRangeCalculation();
    this.applyTags();
    this.getProjections();
  }

  onCloseSideBar(isSubmit) {
    this.openFilter = false;
    if (!isSubmit && this.cacheFilter.getCacheFilters('Compare-PL')) {
      this.dataFilter = this.cacheFilter.getCacheFilters('Compare-PL');
    }
    if (isSubmit) {
      this.doFilterData();
    }
  }

  // used to take query string param and removes values which are not set in the query string
  queryStringUtil(queryStringParam: QueryFilterParams) {
    const queryFilter: QueryFilterParams = {};
    if (queryStringParam) {
      for (const [key] of Object.entries(queryStringParam)) {
        if (queryStringParam[key] === '' || queryStringParam[key] === null || queryStringParam[key] === undefined) {
          delete queryStringParam[key];
        }
      }
    }
    // inserting order by in the query param
    queryStringParam.order_by = 'asc:customer_name,asc:name';
    return queryStringParam;
  }

  showButton(rowNode) {
    if (rowNode?.level === 1 && rowNode?.node?.parent?.data?.type === AppConstants.grossMargin) {
      return false;
    }
    if (rowNode?.level === 1 && rowNode?.node?.parent?.data?.type === 'Gross Profit') {
      return false;
    }
    if (rowNode?.level !== 2) {
      return true;
    }
    return false;
  }
  resetFilter() {
    this.dataFilter = new IFilter();
    this.cacheFilter.resetCacheFilters('Compare-PL');
    this.tags = [];
    this.dataFilter.rollingOption = 'Current plus 2 months';
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: null }, queryParamsHandling: 'merge' });
    // reset the filter selection as well
    this.selectedFilterFormControl = new FormControl('');
    this.dateRangeCalculation();
    this.defaultFilters();
    this.getProjections();
  }

  openSaveFilterList() {
    this.showFilterListDialog = true;
    this.showSavedFilter = true;
    this.cdf.detectChanges();
  }
  applyAllFilter() {
    if (this.dataFilter) {
      if (this.dataFilter.start_month) {
        this.dataFilter.start_month = moment(this.dataFilter.start_date).toDate();
      }
      if (this.dataFilter.end_month) {
        this.dataFilter.end_month = moment(this.dataFilter.end_date).toDate();
      }
      if (this.dataFilter?.statuses) {
        this.dataFilter.statuses = this.dataFilter.statuses.replace(/%2C/g, ',');
        this.dataFilter.status = this.dataFilter.statuses.split(',');
      }
      if (!this.dataFilter?.statuses) {
        this.dataFilter.statuses = '';
        this.dataFilter.status = [];
      }

      if (this.dataFilter?.project_grp_name && this.dataFilter?.project_grp_value) {
        this.dataFilter.project_name = { name: this.dataFilter.project_grp_name, value: this.dataFilter.project_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&') };
        delete this.dataFilter.project_grp_name;
        delete this.dataFilter.project_grp_value;
      }
      if (this.dataFilter?.client_grp_name && this.dataFilter?.client_grp_value) {
        this.dataFilter.customer_name = { name: this.dataFilter.client_grp_name, value: this.dataFilter.client_grp_value.replace(/%3D/g, '=').replace(/%26/g, '&') };
        delete this.dataFilter.client_grp_name;
        delete this.dataFilter.client_grp_value;
      }
      if (this.dataFilter?.project_ids) {
        this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
        this.dataFilter.showProjectFilter = true;
      }
      if (this.dataFilter?.position_ids) {
        this.dataFilter.position_ids = Number(this.dataFilter.position_ids);
      }
      if (this.dataFilter?.customer_ids) {
        this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
        this.dataFilter.showClientFilter = true;
      }
      if (this.dataFilter?.effective_date1 && this.dataFilter?.variance && this.dataFilter?.showCustomVariancePeriod?.toString() === 'false') {
        this.dataFilter.varianceValue = Number(this.dataFilter.varianceValue);
        this.dataFilter.effective_date1 = new Date(new Date().setDate(new Date().getDate() - this.dataFilter.varianceValue));
        this.dataFilter.effective_date2 = new Date();
        this.dataFilter.showCustomVariancePeriod = false;
      }
      if (this.dataFilter.variance.toString() === 'false') {
        this.dataFilter.effective_date1 = this.dataFilter?.effective_date1
          ? new Date(this.dataFilter?.effective_date1?.toString().replace(/%3A/g, ':'))
          : this.dataFilter?.effective_date1;
        this.dataFilter.effective_date2 = this.dataFilter?.effective_date2 ? new Date(this.dataFilter?.effective_date2?.toString().replace(/%3A/g, ':')) : new Date();
        this.dataFilter.showCustomVariancePeriod = true;
      }
      if (this.dataFilter?.ClientName?.length && this.dataFilter.customer_ids) {
        this.dataFilter.customer_ids = this.dataFilter.customer_ids.replace(/%2C/g, ',');
        this.dataFilter.clientName = this.dataFilter.customer_ids.split(',');
        this.dataFilter.ClientName = this.dataFilter.ClientName.toString().replace(/%2C/g, ',').split(',');
        this.dataFilter.showClientFilter = false;
      }

      if (this.dataFilter?.projectName?.length && this.dataFilter.project_ids) {
        this.dataFilter.project_ids = this.dataFilter.project_ids.replace(/%2C/g, ',');
        this.dataFilter.projectName = this.dataFilter.projectName.toString().replace(/%2C/g, ',').split(',');
        this.dataFilter.showProjectFilter = false;
      }

      if (this.dataFilter?.tags) {
        this.dataFilter.tags = this.dataFilter.tags.replace(/%3A/g, ':').replace(/%2C/g, ',');
      }
    }
    this.applyTags();
  }

  applyFilter() {
    this.showSavedFilter = false;
    let dataFilter = JSON.parse('{"' + decodeURI(this.selectedFilter.query_filter.query_string).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
    if (dataFilter.include_utilizations) {
      dataFilter.include_utilizations = dataFilter.include_utilizations != 'false';
    }
    if (dataFilter.include_pl_plugs) {
      dataFilter.include_pl_plugs = dataFilter.include_pl_plugs != 'false';
    }
    if (dataFilter.selectedClient) {
      const a = dataFilter?.selectedClient?.toString()?.replace(/%2C/g, ',')?.replace(/%3A/g, ':');
      dataFilter.selectedClient = JSON.parse(dataFilter?.selectedClient?.toString()?.replace(/%2C/g, ',')?.replace(/%3A/g, ':'));
    }
    if (dataFilter.selectedProject) {
      dataFilter.selectedProject = JSON.parse(dataFilter?.selectedProject?.toString().replace(/%2C/g, ',').replace(/%3A/g, ':'));
    }
    this.dataFilter = dataFilter;
    this.applyAllFilter();
    this.selectedFilter = null;
    this.filteredFilters.data.query_filters = this.availableFilters;
    this.getProjections();
    this.closeModal();
  }

  getStartDateEndDateFromRolling(rollingOption: string) {
    const currentDate = new Date();
    const rollingRelativeMonth = rollingOption.split(' ')[1];
    const rollingMonths = parseInt(rollingOption.split(' ')[2]);

    const startDate = new Date(currentDate);
    startDate.setDate(1);

    const endDate = new Date(startDate);
    endDate.setMonth(startDate.getMonth() + rollingMonths + 1, 0);

    if (rollingRelativeMonth === 'minus') {
      startDate.setMonth(currentDate.getMonth() - 1);
      endDate.setMonth(currentDate.getMonth() + rollingMonths, 0);
      endDate.setFullYear(currentDate.getFullYear());
    }

    return { startDate, endDate };
  }

  getStartEndDateFromQuarter(quarter) {
    const now = new Date();
    const quarterDates = {
      Q1: {
        start_date: new Date(now.getFullYear(), 0, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 2, 31, 11, 59, 59, 999)
      },
      Q2: {
        start_date: new Date(now.getFullYear(), 3, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 5, 30, 11, 59, 59, 999)
      },
      Q3: {
        start_date: new Date(now.getFullYear(), 6, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 8, 30, 11, 59, 59, 999)
      },
      Q4: {
        start_date: new Date(now.getFullYear(), 9, 1, 0, 0, 0, 0),
        end_date: new Date(now.getFullYear(), 11, 31, 11, 59, 59, 999)
      }
    };
    return quarterDates[quarter];
  }

  getStartEndDateFromYear(year) {
    return {
      start_date: new Date(year, 0, 1, 0, 0, 0, 0),
      end_date: new Date(year, 11, 31, 11, 59, 59, 999)
    };
  }

  closeModal() {
    this.showFilterListDialog = false;
    this.selectedFilter = null;
    this.showEditDialog = false;
    this.editFilterObj = null;
    this.showNameError = false;
    this.showDeleteDialog = false;
    this.deleteFilterObj = null;
    this.showShareDialog = false;
    this.shareFilterObj = null;
  }

  onSaveFilter() {
    let filter = JSON.parse(JSON.stringify(this.dataFilter));
    filter = this.queryStringUtil(filter);
    if (filter.selectedClient) {
      filter.selectedClient = JSON.stringify(filter.selectedClient);
    }
    if (filter.selectedProject) {
      filter.selectedProject = JSON.stringify(filter.selectedProject);
    }
    if (this.dataFilter?.project_name?.name) {
      filter.project_grp_name = this.dataFilter?.project_name?.name;
      filter.project_grp_value = this.dataFilter?.project_name?.value;
    }
    if (this.dataFilter?.customer_name?.name) {
      filter.client_grp_name = this.dataFilter?.customer_name?.name;
      filter.client_grp_value = this.dataFilter?.customer_name?.value;
    }

    const requestObject: SaveFilter = {
      query_string: this.serialize(filter),
      resource: 'plReport'
    };
    const dialogTitle = 'Save Filter Group';
    const dialogRef = this.layoutUtilsService.saveClientGroupName(dialogTitle, requestObject);
    dialogRef.afterClosed().subscribe((filtersResponse) => {
      if (filtersResponse) {
        this.getStoredFilters();
        this.layoutUtilsService.showActionNotification('Filter has been saved successfully', AlertType.Success);
      }
    });
  }
  // converts the object in to query param string
  serialize = (obj) => {
    const str = [];
    for (const p in obj) {
      if (p !== 'selectedClient' && p !== 'selectedProject') {
        if (obj.hasOwnProperty(p)) {
          str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
        }
      } else {
        str.push(p + '=' + obj[p]);
      }
    }
    return str.join('&');
  };

  getStoredFilters() {
    const requestObject = {
      resource: 'plReport'
    };
    this.subscriptionManager.add(
      this.utilizationService.getStoredFilters(requestObject).subscribe((res: ISavedFilterList) => {
        this.sharedFilters = [];
        this.myFilters = [];
        this.filteredFilters = JSON.parse(JSON.stringify(res)); // to deep copy the object
        this.sharedFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === true);
        this.myFilters = this.filteredFilters?.data?.query_filters?.filter((q) => q.query_filter.is_shared === false);
        this.availableFilters = res.data.query_filters;
        this.cdf.detectChanges();
        this.routerListener();
      })
    );
  }

  getCustomerInfo(projectId): Promise<Project> {
    return new Promise((resolve) => {
      if (projectId) {
        this.utilizationService.getProject(projectId).subscribe((res) => {
          resolve(res?.data);
        });
      } else {
        resolve(null);
      }
    });
  }

  getSymbol(value, rowNode) {
    if (
      rowNode?.parent?.data?.type === this.appConstants.costOfGoodsSold ||
      rowNode?.parent?.parent?.data?.type === this.appConstants.costOfGoodsSold ||
      rowNode?.parent?.parent?.parent?.data?.type === this.appConstants.costOfGoodsSold ||
      rowNode?.node?.data?.type === this.appConstants.costOfGoodsSold
    ) {
      if (Number(value) > 0) {
        return false;
      } else if (Number(value) < 0) {
        return true;
      } else {
        return null;
      }
    } else {
      if (Number(value) < 0) {
        return false;
      } else if (Number(value) > 0) {
        return true;
      } else {
        return null;
      }
    }
  }

  showToolTip(rowData: any, rowNode: any, projection: string): string {
    let showPercent =
      rowNode?.node?.parent?.data?.type === 'Gross Margin' || rowNode?.node?.data?.type === 'Gross Margin' || rowNode?.node?.data?.type === 'Net Margin' ? true : false;
    if (rowNode?.node?.parent?.parent?.data?.type === 'Projects') {
      const cost = this.getSeparateExpense(rowNode?.node?.data, rowData, projection);
      return `
      <div>
        <div class="row">
          <div class="col-2 p-0">Previous P&L</div>
          <div class="col-6 p-0">${this.effectiveDate1}</div>
          <div class="col-4 p-0 d-flex justify-content-end">${!showPercent ? '$' : ''} ${this.commaNumberPipe.transform(rowData?.value?.effective_date_1)} ${
        showPercent ? '%' : ''
      } </div>
        </div>
        <div class="row">
          <div class="col-2 p-0">Baseline P&L</div>
          <div class="col-6 p-0">${this.effectiveDate2}</div>
          <div class="col-4 p-0 d-flex justify-content-end">${!showPercent ? '$' : ''} ${this.commaNumberPipe.transform(rowData?.value?.effective_date_2)} ${
        showPercent ? '%' : ''
      }</div>
        </div>
        <div class="row">
          <div class="col p-0 d-flex justify-content-end">${!showPercent ? '$' : ''} ${this.commaNumberPipe.transform(rowData?.value?.diff)} ${showPercent ? '%' : ''}</div>
        </div>
        <div class="row">
          <div class="col-6 pl-2">
            <div class="d-flex justify-content-start p-0">Direct Labor: $ ${this.commaNumberPipe.transform(cost?.direct_cost) || 0}</div>
            <div class="d-flex justify-content-start p-0">Contract Labor: $ ${this.commaNumberPipe.transform(cost?.contract_cost) || 0}</div>
          </div>
          <div class="col-6 p-0">
              <div class="d-flex justify-content-start p-0">Open Positions Labor: $ ${this.commaNumberPipe.transform(cost?.open_positions_cost) || 0}</div>
              <div class="d-flex justify-content-start p-0">Expenses: $ ${this.commaNumberPipe.transform(cost?.expenses) || 0}</div>
          </div>
        </div>
      </div>
    `;
    } else {
      return `
      <div>
        <div class="row">
          <div class="col-2 p-0">Previous P&L</div>
          <div class="col-6 p-0">${this.effectiveDate1}</div>
          <div class="col-4 p-0 d-flex justify-content-end">${!showPercent ? '$' : ''} ${this.commaNumberPipe.transform(rowData?.value?.effective_date_1)} ${
        showPercent ? '%' : ''
      } </div>
      </div>
      <div class="row">
        <div class="col-2 p-0">Baseline P&L</div>
        <div class="col-6 p-0">${this.effectiveDate2}</div>
        <div class="col-4 p-0 d-flex justify-content-end">${!showPercent ? '$' : ''} ${this.commaNumberPipe.transform(rowData?.value?.effective_date_2)} ${
        showPercent ? '%' : ''
      }</div>
      </div>
      <div class="row">
        <div class="col p-0 d-flex justify-content-end">${!showPercent ? '$' : ''} ${this.commaNumberPipe.transform(rowData?.value?.diff)} ${showPercent ? '%' : ''}</div>
      </div>
    `;
    }
  }
  searchFilters(filter: string) {
    this.filteredFilters.data.query_filters = filter.length
      ? this.availableFilters.filter((availableFilter) => availableFilter.query_filter.name.toLowerCase().includes(filter.toLowerCase()))
      : this.availableFilters;
    this.sharedFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === true);
    this.myFilters = this.filteredFilters?.data?.query_filters.filter((q) => q.query_filter.is_shared === false);
  }

  editFilter(filter) {
    this.showEditDialog = true;
    this.editFilterObj = _.cloneDeep(filter);
  }
  shareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = { ...filterOption, header: 'Share', text: `Do you want to share Filter "${filterOption.query_filter.name}" to all users publically?` };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  unShareFilter(filterOption) {
    this.showShareDialog = true;
    this.shareFilterObj = { ...filterOption, header: 'Unshare', text: `Do you want to unshare Filter "${filterOption.query_filter.name}" from all users?` };
    this.shareFilterObj.query_filter.is_shared = !this.shareFilterObj.query_filter.is_shared;
  }

  saveShareFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.updateFilter(this.shareFilterObj.query_filter.id, this.shareFilterObj).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(this.shareFilterObj.query_filter.is_shared ? AppConstants.shareFilter : AppConstants.unShareFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.utilizationService.showNewSharedFilter.next('P&L Comparison');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }
  inputFilterName() {
    this.showNameError = false;
  }

  deleteFilter(filterOption) {
    this.showDeleteDialog = true;
    this.deleteFilterObj = filterOption;
  }

  saveDeleteFilter() {
    this.isSubmitting = true;
    this.subscriptionManager.add(
      this.utilizationService.deleteStoredFilters(this.deleteFilterObj.query_filter.id).subscribe(
        (res) => {
          this.layoutUtilsService.showActionNotification(AppConstants.deleteFilter, AlertType.Success);
          this.isSubmitting = false;
          this.closeModal();
          this.getStoredFilters();
          this.showSavedFilter = true;
          this.showFilterListDialog = true;
          this.utilizationService.showNewSharedFilter.next('P&L Comparison');
          this.cdf.detectChanges();
        },
        () => (this.isSubmitting = false)
      )
    );
  }

  saveEditFilter() {
    if (!this.editFilterObj.query_filter.name.length) {
      this.showNameError = true;
    } else {
      this.isSubmitting = true;
      this.subscriptionManager.add(
        this.utilizationService.updateFilter(this.editFilterObj.query_filter.id, this.editFilterObj).subscribe(
          (res) => {
            this.layoutUtilsService.showActionNotification(AppConstants.updateFilter, AlertType.Success);
            this.isSubmitting = false;
            this.closeModal();
            this.getStoredFilters();
            this.showSavedFilter = true;
            this.showFilterListDialog = true;
            this.cdf.detectChanges();
          },
          () => (this.isSubmitting = false)
        )
      );
    }
  }

  private routerListener() {
    this.activatedRoute.queryParamMap.subscribe((params: Params) => {
      // grab the filter id from the url if present we will make an api call to get the specific filter from that id.
      if (params.params.filterId) {
        this.queryFilterId = params.params.filterId;
        this.getTheFilterById();
      }
    });
  }

  // used to get the filter by its id
  getTheFilterById() {
    this.subscriptionManager.add(
      this.projectService.getTheFilterById(this.queryFilterId).subscribe(
        (res) => {
          // if we get some response only then we may try to apply filter
          if (res) {
            this.selectedFilter = res?.data;
            const filterValue = this.sharedFilters?.find((f) => JSON.stringify(f) === JSON.stringify(this.selectedFilter));
            if (filterValue) {
              this.selectedFilterFormControl.setValue(filterValue);
            }
            this.applyFilter();
          }
        },
        (error) => {
          this.layoutUtilsService.showActionNotification(AppConstants.problemFetchingFilterById, AlertType.Error);
        }
      )
    );
  }

  copyLinkToTheFilter(filterId: number) {
    const filterHolder = document.createElement('textarea');
    filterHolder.style.position = 'fixed';
    filterHolder.style.left = '0';
    filterHolder.style.top = '0';
    filterHolder.style.opacity = '0';
    // construct the full url to be copied
    // e.g. http://localhost:4200/project/manage?filterId=3
    const hostName = `${window.location.protocol}${window.location.host}`;
    const filterString = `${hostName}${this.router.url.split('?')[0]}/?filterId=${filterId}`;

    filterHolder.value = filterString;
    document.body.appendChild(filterHolder);
    filterHolder.focus();
    filterHolder.select();
    document.execCommand('copy');
    document.body.removeChild(filterHolder);
    this.layoutUtilsService.showActionNotification(AppConstants.filterLinkCopied, AlertType.Success);
  }

  applySelectedFilterAndUpdateUrl() {
    this.showSavedFilter = false;
    this.selectedFilter = this.selectedFilterFormControl.value;
    this.router.navigate([], { relativeTo: this.activatedRoute, queryParams: { filterId: this.selectedFilter?.query_filter?.id }, queryParamsHandling: 'merge' });
  }

  getEffectiveDates(queryFilter): Promise<void> {
    return new Promise((resolve) => {
      this.subscriptionManager.add(
        this.projectService.getProjectEffectiveDates(queryFilter).subscribe((res) => {
          if (res?.dates?.length) {
            const firstIndexDate = [...res.dates].shift();
            this.minEffectiveDate = moment(firstIndexDate).toDate();
          } else {
            this.layoutUtilsService.showActionNotification(AppMessages.noResultForCriteria, AlertType.Error);
          }
          resolve();
        })
      );
    });
  }

  private dialogResolve: (value: boolean) => void;

  getPausedProjectList(projectIds = null): Promise<boolean> {
    let query = { validations: 'NO,PAUSED' };
    if (projectIds) {
      query['project_ids'] = projectIds;
    }
    return new Promise<boolean>((resolve) => {
      this.subscriptionManager.add(
        this.adminService.getPausedProjectList(query).subscribe(
          (res: ProjectList) => {
            this.loading$.next(false);
            if (res?.data?.projects?.length) {
              this.pausedProjectList = res.data.projects;
              this.showPausedProjectDialog = true;
              this.dialogResolve = resolve;
            } else {
              resolve(true);
            }
          },
          () => {
            this.loading$.next(false);
            resolve(true);
          }
        )
      );
    });
  }

  continue(): void {
    this.showPausedProjectDialog = false;
    if (this.dialogResolve) {
      this.dialogResolve(true);
      this.dialogResolve = null;
    }
  }

  async wait() {
    this.isResumeValidationInProgress = true;
    const validationPromises = this.pausedProjectList.map(
      (project) =>
        new Promise<void>(async (resolve) => {
          this.calculatingProjectName = project?.project?.name;
          this.startRotatingMessages();
          this.startRotatingProjectName();
          this.resumeProjectValidation(project.project.id);
          const projectObj = await this.checkValidationStatus(project.project.id);

          if (projectObj?.validated === 'YES') {
            resolve();
          } else {
            const intervalId = setInterval(async () => {
              const updatedProjectObj = await this.checkValidationStatus(project.project.id);
              if (updatedProjectObj?.validated === 'YES') {
                clearInterval(intervalId);
                resolve();
              }
            }, 4000);
          }
        })
    );

    Promise.all(validationPromises).then(() => {
      if (this.dialogResolve) {
        this.isResumeValidationInProgress = false;
        this.showPausedProjectDialog = false;
        this.dialogResolve(true);
        this.dialogResolve = null;
      }
    });
  }

  resumeProjectValidation(projectId: number): void {
    this.subscriptionManager.add(
      this.projectService.resumeProjectValidation(projectId).subscribe({
        complete: () => {
          //TODO: Handle completion if needed
        }
      })
    );
  }

  checkValidationStatus(id: number): Promise<any> {
    return new Promise((resolve, reject) => {
      if (id) {
        this.subscriptionManager.add(
          this.projectService.getValidationStatus(id).subscribe({
            next: (res) => {
              resolve(res.data.project); // Resolve with the data
            },
            error: (err) => {
              reject(err); // Reject in case of error
            }
          })
        );
      } else {
        reject();
      }
    });
  }

  private startRotatingMessages(): void {
    this.currentMessageIndex = 0;
    this.updateCurrentMessage();

    if (this.messageInterval) {
      this.messageInterval.unsubscribe();
    }

    this.messageInterval = interval(2000).subscribe(() => {
      this.currentMessageIndex = (this.currentMessageIndex + 1) % AppConstants.RECALCULATION_MESSAGES.length;
      this.updateCurrentMessage();
    });
  }

  private updateCurrentMessage(): void {
    this.rotatingMessage = AppConstants.RECALCULATION_MESSAGES[this.currentMessageIndex];
  }

  private startRotatingProjectName(): void {
    let projectIndex = 0;
    const projectNameInterval = interval(10000).subscribe(() => {
      projectIndex = (projectIndex + 1) % this.pausedProjectList.length;
      this.calculatingProjectName = this.pausedProjectList[projectIndex]?.project?.name;
    });
  }

  checkNetMarginPermission(): void {
    this.authService
      .isPermittedAction([this.permissionModules.VIEW_NET_MARGIN])
      .then((isPermitted) => {
        this.isAllowedNetMargin = !!isPermitted;
        this.cdf.detectChanges();
      })
      .catch((error) => {
        this.isAllowedNetMargin = false;
      });
  }

  private getSeparateExpense(rowData: any, monthlyExpense: any, projection: string): any {
    let matchingPosition = [];
    const data = this.projections.projections.find((obj) => obj.hasOwnProperty(projection));
    data[projection]?.projects?.find((project) => {
      return project.project.projection.valid_monthly_projections.some(({ valid_monthly_projection }) => {
        const key = `${valid_monthly_projection.year}${String(valid_monthly_projection.month).padStart(2, '0')}`;
        if (key !== monthlyExpense.key) return false;

        const match = valid_monthly_projection.validated_monthly_positions.find(({ validated_monthly_position: pos }) => {
          const emp = pos.position?.employee;
          const positionName = pos.position?.name?.toLowerCase();
          const fullName = emp ? `${emp.first_name} ${emp.last_name}`.toLowerCase() : '';

          const isMatched = positionName === rowData?.type?.toLowerCase() && (!emp || fullName === rowData?.name?.toLowerCase());

          if (isMatched) {
            matchingPosition.push(pos);
            return true;
          }

          return false;
        });

        return !!match;
      });
    });

    if (matchingPosition.length) {
      return matchingPosition[0];
    } else {
      return;
    }
  }

  getSeparateExpenseForToolTip(rowNode: any, rowData: any, projection: string): string {
    const cost = this.getSeparateExpense(rowNode?.node?.data, rowData, projection);
    return `Direct Cost : ${this.commaNumberPipe.transform(cost?.direct_cost) || 0.0}<br>
              Contract Cost : ${this.commaNumberPipe.transform(cost?.contract_cost) || 0.0}<br>
              Open Positions Cost : ${this.commaNumberPipe.transform(cost?.open_positions_cost) || 0.0}<br>
              Expenses : ${this.commaNumberPipe.transform(cost?.expenses) || 0.0}`;
  }

  isPartiallyMatch(input: string): boolean {
    return [this.appConstants.apiMessage.NoResultFound, this.appConstants.apiMessage.noDataFound].some((item) => input.toLowerCase().includes(item.toLowerCase()));
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
      this.onCloseSideBar(true);
    } else if (event.key === 'Escape') {
      event.preventDefault();
      this.onCloseSideBar(false);
    }
  }
  // It check if the value is a margin value or not for $ or % symbols
  isMarginValue(field: string): boolean {
    return field === AppConstants.grossMargin || field === AppConstants.netMargin;
  }

  openExportOptionList() {
    this.showExportOptionDialog = true;
    this.showExportOptions = true;
    this.cdf.detectChanges();
  }
}
